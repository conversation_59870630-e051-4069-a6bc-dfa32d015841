#!/usr/bin/env python3
"""
简单的HTTP服务器，用于测试洪水可视化系统
"""

import http.server
import socketserver
import os
import webbrowser
from urllib.parse import urlparse

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头，允许跨域请求
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_GET(self):
        # 处理根路径，重定向到可视化页面
        if self.path == '/':
            self.path = '/flood-visualization/index.html'

        return super().do_GET()

def start_server(port=8000):
    """启动HTTP服务器"""

    # 确保在正确的目录中
    if not os.path.exists('flood-visualization'):
        print("错误：找不到 flood-visualization 目录")
        print("请确保在正确的项目根目录中运行此脚本")
        return

    if not os.path.exists('processed_data'):
        print("错误：找不到 processed_data 目录")
        print("请先运行 test_coordinate_transform.py 生成测试数据")
        return

    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print(f"🌊 洪水可视化服务器启动成功!")
            print(f"📍 服务器地址: http://localhost:{port}")
            print(f"🎯 可视化页面: http://localhost:{port}/flood-visualization/")
            print(f"📊 数据接口: http://localhost:{port}/processed_data/")
            print("\n按 Ctrl+C 停止服务器")

            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}/flood-visualization/')
                print("🚀 已自动打开浏览器")
            except:
                print("💡 请手动在浏览器中打开上述地址")

            httpd.serve_forever()

    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"错误：端口 {port} 已被占用")
            print(f"请尝试使用其他端口：python simple_server.py --port 8001")
        else:
            print(f"服务器启动失败: {e}")
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='洪水可视化HTTP服务器')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口 (默认: 8000)')

    args = parser.parse_args()
    start_server(args.port)