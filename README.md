# 城市内涝模拟数据可视化系统

基于MapLibre GL JS开发的大规模洪水模拟数据时序可视化系统，支持CGCS2000坐标系转换、实时动画播放和交互式数据查询。

## 🌟 主要特性

- **高性能渲染**: 基于MapLibre GL JS的WebGL渲染，支持大规模数据可视化
- **坐标系转换**: 自动处理CGCS2000高斯-克吕格投影到WGS84的坐标转换
- **时序动画**: 流畅的时间序列动画播放，支持速度调节和循环播放
- **交互式查询**: 点击网格查看详细的水深和流速信息
- **多图层支持**: 水深热力图、流速矢量图等多种可视化方式
- **响应式设计**: 适配不同屏幕尺寸的现代化界面

## 📁 项目结构

```
neilao2d/
├── 2D_CELL.shp              # Shapefile几何数据
├── 2D_CELL.prj              # 坐标系定义文件
├── cell_res.csv             # 时间序列数据
├── data_processor.py        # 完整数据预处理脚本
├── test_coordinate_transform.py  # 简化测试脚本
├── simple_server.py         # HTTP服务器
├── processed_data/          # 处理后的数据
│   └── sample_time_series.json
├── flood-visualization/     # 前端可视化系统
│   ├── index.html          # 主页面
│   ├── package.json        # 项目配置
│   └── src/
│       └── main.js         # 主程序
└── README.md
```

## 🚀 快速开始

### 1. 数据预处理

首先运行数据预处理脚本，生成优化的数据格式：

```bash
# 运行简化版测试（推荐用于快速测试）
python test_coordinate_transform.py

# 或运行完整版处理（需要安装geopandas等依赖）
python data_processor.py
```

### 2. 启动可视化服务

```bash
# 启动HTTP服务器
python simple_server.py

# 或指定端口
python simple_server.py --port 8001
```

### 3. 访问可视化界面

服务器启动后会自动打开浏览器，或手动访问：
- 主页面: http://localhost:8000/flood-visualization/
- 数据接口: http://localhost:8000/processed_data/

## 🎮 使用说明

### 界面控制

- **播放控制**: 点击播放按钮开始/暂停动画
- **时间轴**: 拖动滑块跳转到指定时间
- **透明度**: 调节洪水图层的透明度
- **播放速度**: 调节动画播放速度（0.5x - 3x）
- **图层开关**: 控制水深和流速图层的显示

### 交互功能

- **点击查询**: 点击任意网格查看详细信息
- **地图导航**: 支持缩放、平移等标准地图操作
- **数据面板**: 显示选中网格的实时数据

## 🛠 技术架构

### 前端技术栈

- **MapLibre GL JS 4.0**: 开源地图渲染引擎
- **D3.js**: 数据可视化和颜色插值
- **原生JavaScript**: 无框架依赖，轻量高效

### 数据处理

- **Python**: 数据预处理和坐标转换
- **Proj4**: 坐标系转换库
- **GeoPandas**: 空间数据处理（可选）

### 坐标系转换

- **源坐标系**: CGCS2000_3_degree_Gauss_Kruger_CM_108E
- **目标坐标系**: WGS84 (EPSG:4326)
- **转换方法**: Proj4投影变换

## 📊 数据格式

### 输入数据

1. **Shapefile**: 包含网格几何信息
   - `JUNNAME`: 网格单元ID
   - `ELEVATION`: 地面高程
   - `AREA`: 网格面积

2. **CSV时间序列**: 包含模拟结果
   - `cell_name`: 网格单元ID
   - `time_series`: 时间戳
   - `cell_depth`: 水深值（米）
   - `cell_vel`: 流速值（米/秒）

### 输出数据

- **GeoJSON**: 转换后的几何数据
- **JSON**: 优化的时间序列数据
- **元数据**: 边界框、时间范围等信息

## 🎨 可视化方案

### 水深可视化

- **颜色映射**: 蓝色(浅水) → 红色(深水)
- **分级渲染**: 6级颜色分类
- **透明度控制**: 支持动态调节

### 流速可视化

- **矢量箭头**: 显示流向和流速大小
- **颜色编码**: 根据流速大小着色
- **密度控制**: 根据缩放级别调节显示密度

## ⚡ 性能优化

### 数据优化

- **空间索引**: R-tree空间索引加速查询
- **时间分片**: 按时间步分割数据
- **几何简化**: 根据缩放级别简化几何

### 渲染优化

- **WebGL加速**: 利用GPU进行高性能渲染
- **视锥裁剪**: 只渲染可见区域
- **批量更新**: 减少DOM操作次数

### 内存管理

- **数据缓存**: LRU缓存策略
- **按需加载**: 动态加载时间步数据
- **垃圾回收**: 及时释放不用的数据