/**
 * 基于MapTalks的城市内涝可视化系统
 */

class FloodVisualizationMapTalks {
    constructor() {
        this.map = null;
        this.timeSeriesData = null;
        this.geometryData = null;
        this.currentTimeIndex = 0;
        this.isPlaying = false;
        this.playSpeed = 1.0;
        this.animationId = null;
        this.timeSteps = [];
        this.floodLayer = null;
        this.heatmapLayer = null;
        this.smoothLayer = null;
        this.contourLayer = null;
        this.polygons = [];
        this.smoothPolygons = [];
        this.renderMode = 'grid'; // 'grid', 'heatmap', 'smooth', 'contour'
        this.cellCenters = new Map(); // 缓存网格中心点坐标
        this.heatmapUpdateTimeout = null; // 热力图更新防抖
        this.zoomingTimeout = null; // 缩放过程中的节流

        this.init();
    }

    async init() {
        try {
            console.log('MapTalks: 开始初始化系统...');

            // 初始化地图
            this.initMap();

            // 加载数据
            await this.loadData();

            // 设置事件监听
            this.setupEventListeners();

            // 隐藏加载界面
            document.getElementById('loading').style.display = 'none';

            console.log('MapTalks: 洪水可视化系统初始化完成');
        } catch (error) {
            console.error('MapTalks: 初始化失败:', error);
            this.showError('系统初始化失败: ' + error.message);
        }
    }

    initMap() {
        console.log('MapTalks: 初始化地图...');

        // 检查热力图插件是否加载
        console.log('MapTalks: 检查插件加载状态...');
        console.log('maptalks.HeatLayer:', typeof maptalks.HeatLayer);
        console.log('Available maptalks properties:', Object.keys(maptalks));

        // 创建地图 - 使用CartoDB深色底图
        this.map = new maptalks.Map('map', {
            center: [108.9, 34.2],
            zoom: 12,
            baseLayer: new maptalks.TileLayer('base', {
                urlTemplate: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',
                subdomains: ['a', 'b', 'c', 'd'],
                attribution: '&copy; <a href="http://osm.org">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/">CARTO</a>'
            })
        });

        // 创建矢量图层用于显示洪水数据
        this.floodLayer = new maptalks.VectorLayer('flood', {
            enableSimplify: false,
            enableAltitude: false
        }).addTo(this.map);

        // 创建光滑图层
        this.smoothLayer = new maptalks.VectorLayer('smooth', {
            enableSimplify: false,
            enableAltitude: false
        }).addTo(this.map);

        // 初始时隐藏光滑图层
        this.smoothLayer.hide();

        // 创建等值面图层
        this.contourLayer = new maptalks.VectorLayer('contour', {
            enableSimplify: false,
            enableAltitude: false
        }).addTo(this.map);

        // 初始时隐藏等值面图层
        this.contourLayer.hide();

        // 创建热力图层（如果插件可用）
        if (typeof maptalks.HeatLayer === 'function') {
            console.log('MapTalks: 创建热力图层...');
            // 使用固定的精细参数初始化，模拟17级的显示效果
            this.heatmapLayer = new maptalks.HeatLayer('heatmap', [], {
                max: 1.0,
                blur: 5,         // 更小的模糊度，更精细
                radius: 8,       // 更小的半径，更精确
                minOpacity: 0.05, // 更低的最小透明度，更多细节
                gradient: {
                    0.0: 'rgba(0,0,0,0)',    // 完全透明
                    0.1: '#0066cc',          // 深蓝
                    0.2: '#0099ff',          // 蓝色
                    0.3: '#33ccff',          // 浅蓝
                    0.4: '#66ffff',          // 青色
                    0.5: '#00ff00',          // 绿色
                    0.6: '#ffff00',          // 黄色
                    0.8: '#ff9900',          // 橙色
                    0.9: '#ff6600',          // 橙红
                    1.0: '#ff0000'           // 红色
                }
            }).addTo(this.map);

            console.log(`MapTalks: 热力图初始化 - 固定17级精细模式 - 半径: 8px, 模糊: 5px, 最小透明度: 0.05`);

            // 初始时隐藏热力图层
            this.heatmapLayer.hide();
            console.log('MapTalks: 热力图层创建成功');
        } else {
            console.warn('MapTalks: 热力图插件未加载，将使用网格模式');
            this.heatmapLayer = null;
        }

        // 移除所有缩放事件监听，保持热力图参数完全固定
        // 不再监听 zoomend 和 zooming 事件

        console.log('MapTalks: 地图初始化完成');
    }

    async loadData() {
        try {
            console.log('MapTalks: 开始加载数据...');

            // 加载时间序列数据 (使用完整数据)
            const timeResponse = await fetch('../processed_data/full_time_series.json');
            if (!timeResponse.ok) {
                throw new Error(`时间序列数据加载失败! status: ${timeResponse.status}`);
            }

            this.timeSeriesData = await timeResponse.json();
            this.timeSteps = Object.keys(this.timeSeriesData).sort();
            console.log(`MapTalks: 加载了 ${this.timeSteps.length} 个时间步的数据`);

            // 加载几何数据 (使用完整数据)
            const geomResponse = await fetch('../processed_data/full_geometry.geojson');
            if (!geomResponse.ok) {
                throw new Error(`几何数据加载失败! status: ${geomResponse.status}`);
            }

            this.geometryData = await geomResponse.json();
            console.log(`MapTalks: 加载了几何数据: ${this.geometryData.features.length} 个网格`);

            // 更新时间滑块
            const timeSlider = document.getElementById('timeSlider');
            timeSlider.max = this.timeSteps.length - 1;

            // 检查几何数据格式
        console.log('MapTalks: 检查几何数据格式...');
        if (this.geometryData.features.length > 0) {
            const firstFeature = this.geometryData.features[0];
            console.log('第一个特征:', firstFeature);
            console.log('坐标结构:', firstFeature.geometry.coordinates);
            console.log('属性:', firstFeature.properties);
        }

        // 创建多边形
            this.createPolygons();

            // 初始化第一帧
            this.updateVisualization(0);

            console.log('MapTalks: 数据加载完成');

        } catch (error) {
            console.error('MapTalks: 数据加载失败:', error);
            throw error;
        }
    }

    createPolygons() {
        console.log('MapTalks: 创建多边形...');

        this.polygons = [];

        this.geometryData.features.forEach((feature, index) => {
            const coordinates = feature.geometry.coordinates[0];
            const cellId = feature.properties.cell_id;

            // 预计算并缓存网格中心点（用于热力图）
            const centerLng = coordinates.reduce((sum, coord) => sum + coord[0], 0) / coordinates.length;
            const centerLat = coordinates.reduce((sum, coord) => sum + coord[1], 0) / coordinates.length;
            this.cellCenters.set(cellId, [centerLng, centerLat]);

            // 创建多边形
            const polygon = new maptalks.Polygon(coordinates, {
                properties: {
                    cellId: cellId,
                    depth: 0,
                    velocity: 0
                },
                symbol: {
                    lineColor: '#ffffff',
                    lineWidth: 1,
                    lineOpacity: 0.8,
                    polygonFill: '#e0e0e0',
                    polygonOpacity: 0.6
                }
            });

            // 添加点击事件
            polygon.on('click', (e) => {
                this.handlePolygonClick(e.target);
            });

            this.polygons.push(polygon);
        });

        // 添加到图层
        this.floodLayer.addGeometry(this.polygons);

        console.log(`MapTalks: 创建了 ${this.polygons.length} 个多边形`);

        // 暂时禁用光滑多边形创建，避免NaN错误
        // this.createSmoothPolygons();

        // 缩放到数据范围
        this.fitToDataBounds();
    }

    createSmoothPolygons() {
        console.log('MapTalks: 创建光滑多边形...');

        this.smoothPolygons = [];

        this.geometryData.features.forEach((feature, index) => {
            try {
                const coordinates = feature.geometry.coordinates[0];
                const cellId = feature.properties.cell_id;

                // 验证坐标数据
                if (!coordinates || coordinates.length === 0) {
                    console.warn(`MapTalks: 网格 ${cellId} 坐标数据为空`);
                    return;
                }

                // 计算网格中心点，添加验证
                let centerLng = 0;
                let centerLat = 0;
                let validCoords = 0;

                coordinates.forEach(coord => {
                    if (coord && coord.length >= 2 && !isNaN(coord[0]) && !isNaN(coord[1])) {
                        centerLng += coord[0];
                        centerLat += coord[1];
                        validCoords++;
                    }
                });

                if (validCoords === 0) {
                    console.warn(`MapTalks: 网格 ${cellId} 没有有效坐标`);
                    return;
                }

                centerLng = centerLng / validCoords;
                centerLat = centerLat / validCoords;

                // 验证计算结果
                if (isNaN(centerLng) || isNaN(centerLat)) {
                    console.warn(`MapTalks: 网格 ${cellId} 中心点计算结果为NaN: [${centerLng}, ${centerLat}]`);
                    return;
                }

                // 创建圆形来模拟光滑效果
                const circle = new maptalks.Circle([centerLng, centerLat], 30, {
                    properties: {
                        cellId: cellId,
                        depth: 0,
                        velocity: 0
                    },
                    symbol: {
                        lineWidth: 0,
                        polygonFill: '#e0e0e0',
                        polygonOpacity: 0.6
                    }
                });

                this.smoothPolygons.push(circle);

            } catch (error) {
                console.error(`MapTalks: 创建光滑多边形失败，索引 ${index}:`, error);
            }
        });

        // 添加到光滑图层
        if (this.smoothPolygons.length > 0) {
            this.smoothLayer.addGeometry(this.smoothPolygons);
            console.log(`MapTalks: 创建了 ${this.smoothPolygons.length} 个光滑多边形`);
        } else {
            console.error('MapTalks: 没有创建任何光滑多边形');
        }
    }

    getColorByDepth(depth) {
        // 根据水深返回颜色 (适应0-20米的范围)
        if (depth <= 0) return '#e0e0e0';        // 无水：浅灰色
        if (depth <= 0.1) return '#87CEEB';      // 0-10cm：天蓝色
        if (depth <= 0.3) return '#4169E1';      // 10-30cm：皇家蓝
        if (depth <= 0.5) return '#0000FF';      // 30-50cm：蓝色
        if (depth <= 1.0) return '#00FFFF';      // 0.5-1m：青色
        if (depth <= 2.0) return '#00FF00';      // 1-2m：绿色
        if (depth <= 5.0) return '#FFFF00';      // 2-5m：黄色
        if (depth <= 10.0) return '#FFA500';     // 5-10m：橙色
        if (depth <= 15.0) return '#FF4500';     // 10-15m：橙红色
        return '#FF0000';                        // >15m：红色
    }

    updateVisualization(timeIndex) {
        if (!this.timeSeriesData || !this.polygons.length) {
            console.warn('MapTalks: 数据未加载完成');
            return;
        }

        this.currentTimeIndex = timeIndex;
        const timeStep = this.timeSteps[timeIndex];
        const timeData = this.timeSeriesData[timeStep];

        console.log(`MapTalks: 更新时间步 ${timeIndex}: ${timeStep}, 数据网格数: ${timeData.cells.length}`);

        // 创建网格ID到数据的映射
        const dataMap = {};
        timeData.cells.forEach((cellId, index) => {
            dataMap[cellId] = {
                depth: timeData.depths[index],
                velocity: timeData.velocities[index]
            };
        });

        // 先计算统计信息
        let matchedCount = 0;
        let totalDepth = 0;
        let maxDepth = 0;
        let hasDataCount = 0;

        // 遍历所有网格计算统计信息
        this.geometryData.features.forEach(feature => {
            const cellId = feature.properties.cell_id;
            if (dataMap[cellId]) {
                const depth = dataMap[cellId].depth;
                const velocity = dataMap[cellId].velocity;
                matchedCount++;
                totalDepth += depth;
                maxDepth = Math.max(maxDepth, depth);

                if (depth > 0) {
                    hasDataCount++;
                }
            }
        });

        // 根据渲染模式更新显示
        if (this.renderMode === 'heatmap' && this.heatmapLayer) {
            // 使用防抖机制避免频繁更新热力图
            if (this.heatmapUpdateTimeout) {
                clearTimeout(this.heatmapUpdateTimeout);
            }
            this.heatmapUpdateTimeout = setTimeout(() => {
                this.updateHeatmapVisualization(dataMap, matchedCount, totalDepth, maxDepth, hasDataCount);
            }, 50); // 50ms防抖延迟
        } else if (this.renderMode === 'contour') {
            this.updateContourVisualization(dataMap, matchedCount, totalDepth, maxDepth, hasDataCount);
        } else if (this.renderMode === 'smooth') {
            this.updateSmoothVisualization(dataMap, matchedCount, totalDepth, maxDepth, hasDataCount);
        } else {
            this.updateGridVisualization(dataMap, matchedCount, totalDepth, maxDepth, hasDataCount);
        }

        const avgDepth = matchedCount > 0 ? (totalDepth / matchedCount) : 0;
        console.log(`MapTalks: 匹配网格数: ${matchedCount}/${this.polygons.length}, 有水深网格: ${hasDataCount}, 平均水深: ${avgDepth.toFixed(3)}m, 最大水深: ${maxDepth.toFixed(3)}m`);

        // 更新时间显示
        this.updateTimeDisplay(timeStep);

        // 更新滑块位置
        document.getElementById('timeSlider').value = timeIndex;
    }

    updateGridVisualization(dataMap, matchedCount, totalDepth, maxDepth, hasDataCount) {
        // 更新每个多边形的颜色
        this.polygons.forEach(polygon => {
            const cellId = polygon.getProperties().cellId;
            let depth = 0;
            let velocity = 0;

            if (dataMap[cellId]) {
                depth = dataMap[cellId].depth;
                velocity = dataMap[cellId].velocity;

                if (depth > 0) {
                    hasDataCount++;
                }
            }

            // 更新多边形属性
            polygon.setProperties({
                cellId: cellId,
                depth: depth,
                velocity: velocity
            });

            // 更新颜色
            const color = this.getColorByDepth(depth);
            const opacity = depth > 0 ? 0.8 : 0.3;

            polygon.updateSymbol({
                polygonFill: color,
                polygonOpacity: opacity
            });
        });
    }

    updateSmoothVisualization(dataMap, matchedCount, totalDepth, maxDepth, hasDataCount) {
        // 更新每个光滑多边形的颜色
        this.smoothPolygons.forEach(circle => {
            const cellId = circle.getProperties().cellId;
            let depth = 0;
            let velocity = 0;

            if (dataMap[cellId]) {
                depth = dataMap[cellId].depth;
                velocity = dataMap[cellId].velocity;
            }

            // 更新圆形属性
            circle.setProperties({
                cellId: cellId,
                depth: depth,
                velocity: velocity
            });

            // 更新颜色和大小
            const color = this.getColorByDepth(depth);
            const opacity = depth > 0 ? 0.7 : 0.2;
            const radius = depth > 0 ? 30 + (depth * 20) : 20; // 根据水深调整半径

            circle.updateSymbol({
                polygonFill: color,
                polygonOpacity: opacity
            });

            // 更新半径
            circle.setRadius(radius);
        });
    }

    updateContourVisualization(dataMap, matchedCount, totalDepth, maxDepth, hasDataCount) {
        console.log('MapTalks: 更新等值面可视化');
        console.log('MapTalks: 数据映射大小:', Object.keys(dataMap).length);
        console.log('MapTalks: 最大深度:', maxDepth);

        // 清空之前的等值面
        this.contourLayer.clear();

        try {
            // 简化版本：直接使用稍微缩放的网格，避免复杂算法
            let polygonCount = 0;

            this.geometryData.features.forEach(feature => {
                const cellId = feature.properties.cell_id;
                if (dataMap[cellId] && dataMap[cellId].depth > 0) {
                    const depth = dataMap[cellId].depth;
                    const coordinates = feature.geometry.coordinates[0];

                    // 验证坐标有效性
                    const validCoords = coordinates.filter(coord =>
                        coord && coord.length >= 2 &&
                        !isNaN(coord[0]) && !isNaN(coord[1]) &&
                        isFinite(coord[0]) && isFinite(coord[1])
                    );

                    if (validCoords.length < 3) {
                        console.warn(`MapTalks: 网格 ${cellId} 坐标无效，跳过`);
                        return;
                    }

                    // 创建圆滑的等值面效果
                    const smoothCoords = this.createSmoothContourCell(validCoords, depth);

                    if (smoothCoords.length < 3) {
                        console.warn(`MapTalks: 网格 ${cellId} 平滑后坐标无效，跳过`);
                        return;
                    }

                    const color = this.getColorByDepth(depth);

                    const contourPoly = new maptalks.Polygon(smoothCoords, {
                        symbol: {
                            lineColor: color,
                            lineWidth: 1,
                            lineOpacity: 0.6,
                            polygonFill: color,
                            polygonOpacity: 0.8
                        },
                        properties: {
                            depth: depth,
                            cellId: cellId,
                            type: 'contour-smooth'
                        }
                    });

                    this.contourLayer.addGeometry(contourPoly);
                    polygonCount++;
                }
            });

            console.log(`MapTalks: 等值面创建了 ${polygonCount} 个简化多边形`);

        } catch (error) {
            console.error('MapTalks: 等值面创建失败:', error);
            // 发生错误时自动切换到网格模式
            this.changeRenderMode('grid');
            document.getElementById('renderModeSelect').value = 'grid';
            this.showError('等值面渲染出现问题，已自动切换到网格模式');
        }

        console.log(`MapTalks: 等值面更新完成，最大深度: ${maxDepth.toFixed(3)}m`);
        console.log(`MapTalks: 等值面图层几何体数量: ${this.contourLayer.getGeometries().length}`);
    }

    createSmoothContourCell(coordinates, depth) {
        // 创建圆滑的等值面单元
        try {
            // 计算网格中心点
            let centerX = 0, centerY = 0;
            coordinates.forEach(coord => {
                centerX += coord[0];
                centerY += coord[1];
            });
            centerX /= coordinates.length;
            centerY /= coordinates.length;

            // 根据水深调整圆滑程度
            const smoothFactor = Math.min(depth * 0.1, 0.8); // 水深越大越圆滑
            const scaleFactor = 0.95 + smoothFactor * 0.1; // 稍微放大

            // 创建圆角矩形效果
            const smoothedCoords = this.createRoundedPolygon(coordinates, centerX, centerY, smoothFactor, scaleFactor);

            return smoothedCoords;

        } catch (error) {
            console.warn('MapTalks: createSmoothContourCell 失败:', error);
            return coordinates;
        }
    }

    createRoundedPolygon(coordinates, centerX, centerY, smoothFactor, scaleFactor) {
        // 使用贝塞尔曲线创建更圆滑的多边形
        const smoothedPoints = [];
        const cornerRadius = 0.00008 * (1 + smoothFactor * 2); // 增大圆角半径

        // 先缩放所有点
        const scaledCoords = coordinates.map(coord => [
            centerX + (coord[0] - centerX) * scaleFactor,
            centerY + (coord[1] - centerY) * scaleFactor
        ]);

        for (let i = 0; i < scaledCoords.length; i++) {
            const prev = scaledCoords[(i - 1 + scaledCoords.length) % scaledCoords.length];
            const current = scaledCoords[i];
            const next = scaledCoords[(i + 1) % scaledCoords.length];

            // 计算到前一个点和下一个点的向量
            const toPrev = [prev[0] - current[0], prev[1] - current[1]];
            const toNext = [next[0] - current[0], next[1] - current[1]];

            // 归一化向量
            const prevLength = Math.sqrt(toPrev[0] * toPrev[0] + toPrev[1] * toPrev[1]);
            const nextLength = Math.sqrt(toNext[0] * toNext[0] + toNext[1] * toNext[1]);

            if (prevLength > 0 && nextLength > 0) {
                const prevNorm = [toPrev[0] / prevLength, toPrev[1] / prevLength];
                const nextNorm = [toNext[0] / nextLength, toNext[1] / nextLength];

                // 计算圆角的控制点
                const offset = Math.min(cornerRadius, Math.min(prevLength, nextLength) * 0.4);

                const controlPoint1 = [
                    current[0] + prevNorm[0] * offset,
                    current[1] + prevNorm[1] * offset
                ];

                const controlPoint2 = [
                    current[0] + nextNorm[0] * offset,
                    current[1] + nextNorm[1] * offset
                ];

                // 创建圆角的贝塞尔曲线点
                const curvePoints = this.createBezierCurve(controlPoint1, current, controlPoint2, 4);

                // 添加曲线点
                curvePoints.forEach(point => {
                    smoothedPoints.push(point);
                });
            } else {
                smoothedPoints.push(current);
            }
        }

        // 闭合多边形
        if (smoothedPoints.length > 0 &&
            (smoothedPoints[0][0] !== smoothedPoints[smoothedPoints.length-1][0] ||
             smoothedPoints[0][1] !== smoothedPoints[smoothedPoints.length-1][1])) {
            smoothedPoints.push(smoothedPoints[0]);
        }

        return smoothedPoints;
    }

    createBezierCurve(p0, p1, p2, segments) {
        // 创建二次贝塞尔曲线
        const points = [];

        for (let i = 0; i <= segments; i++) {
            const t = i / segments;
            const x = (1 - t) * (1 - t) * p0[0] + 2 * (1 - t) * t * p1[0] + t * t * p2[0];
            const y = (1 - t) * (1 - t) * p0[1] + 2 * (1 - t) * t * p1[1] + t * t * p2[1];

            points.push([x, y]);
        }

        return points;
    }

    createMergedContourPolygons(validCells, level) {
        console.log(`MapTalks: 开始精细合并等值面级别 ${level}m 的网格`);

        if (validCells.length === 0) {
            return [];
        }

        // 按深度进一步细分，创建更精细的等值面
        const depthGroups = this.groupCellsByDepthRange(validCells, level);
        const mergedPolygons = [];

        depthGroups.forEach((group, groupIndex) => {
            console.log(`MapTalks: 处理深度组 ${groupIndex}, 包含 ${group.length} 个网格`);

            // 找到相邻的网格并合并
            const clusters = this.findAdjacentClusters(group);

            clusters.forEach(cluster => {
                if (cluster.length === 1) {
                    // 单个网格，直接使用原始坐标但稍微缩放
                    const scaledCoords = this.scalePolygon(cluster[0].coordinates, 0.95);
                    mergedPolygons.push({
                        coordinates: scaledCoords,
                        cellCount: 1,
                        avgDepth: cluster[0].depth
                    });
                } else {
                    // 多个相邻网格，合并边界
                    const mergedCoords = this.mergeAdjacentPolygons(cluster);
                    mergedPolygons.push({
                        coordinates: mergedCoords,
                        cellCount: cluster.length,
                        avgDepth: cluster.reduce((sum, cell) => sum + cell.depth, 0) / cluster.length
                    });
                }
            });
        });

        console.log(`MapTalks: 等值面级别 ${level}m 创建了 ${mergedPolygons.length} 个精细区域`);
        return mergedPolygons;
    }

    groupCellsByDepthRange(validCells, baseLevel) {
        // 在当前等值面级别内，按深度进一步细分
        const groups = new Map();

        validCells.forEach(cell => {
            // 计算深度子级别（每0.1m一个子级别）
            const subLevel = Math.floor(cell.depth * 10) / 10;

            if (!groups.has(subLevel)) {
                groups.set(subLevel, []);
            }
            groups.get(subLevel).push(cell);
        });

        return Array.from(groups.values());
    }

    findAdjacentClusters(cells) {
        // 使用并查集算法找到相邻的网格簇
        const clusters = [];
        const visited = new Set();

        cells.forEach(cell => {
            if (!visited.has(cell.cellId)) {
                const cluster = [];
                this.dfsAdjacentCells(cell, cells, visited, cluster);
                if (cluster.length > 0) {
                    clusters.push(cluster);
                }
            }
        });

        return clusters;
    }

    dfsAdjacentCells(currentCell, allCells, visited, cluster) {
        if (visited.has(currentCell.cellId)) {
            return;
        }

        visited.add(currentCell.cellId);
        cluster.push(currentCell);

        // 找到相邻的网格（简化：基于中心点距离）
        const maxDistance = 0.0003; // 约30米，相邻网格的距离

        allCells.forEach(otherCell => {
            if (!visited.has(otherCell.cellId) &&
                currentCell.center && otherCell.center) {

                const distance = Math.sqrt(
                    Math.pow(currentCell.center[0] - otherCell.center[0], 2) +
                    Math.pow(currentCell.center[1] - otherCell.center[1], 2)
                );

                if (distance <= maxDistance) {
                    this.dfsAdjacentCells(otherCell, allCells, visited, cluster);
                }
            }
        });
    }

    scalePolygon(coordinates, scale) {
        // 验证输入
        if (!coordinates || coordinates.length === 0) {
            console.warn('MapTalks: scalePolygon 收到空坐标');
            return [];
        }

        // 计算多边形中心点，添加NaN检查
        let centerX = 0, centerY = 0, validCount = 0;

        coordinates.forEach(coord => {
            if (coord && coord.length >= 2 && !isNaN(coord[0]) && !isNaN(coord[1])) {
                centerX += coord[0];
                centerY += coord[1];
                validCount++;
            }
        });

        if (validCount === 0) {
            console.warn('MapTalks: scalePolygon 没有有效坐标');
            return coordinates;
        }

        centerX /= validCount;
        centerY /= validCount;

        // 验证中心点
        if (isNaN(centerX) || isNaN(centerY)) {
            console.warn('MapTalks: scalePolygon 中心点计算为NaN');
            return coordinates;
        }

        // 从中心点缩放，添加NaN检查
        return coordinates.map(coord => {
            if (!coord || coord.length < 2 || isNaN(coord[0]) || isNaN(coord[1])) {
                console.warn('MapTalks: scalePolygon 跳过无效坐标:', coord);
                return coord;
            }

            const scaledX = centerX + (coord[0] - centerX) * scale;
            const scaledY = centerY + (coord[1] - centerY) * scale;

            if (isNaN(scaledX) || isNaN(scaledY)) {
                console.warn('MapTalks: scalePolygon 缩放结果为NaN:', coord, '->', [scaledX, scaledY]);
                return coord;
            }

            return [scaledX, scaledY];
        });
    }

    mergeAdjacentPolygons(cluster) {
        // 简化的多边形合并：计算所有网格的边界点
        const allPoints = [];

        cluster.forEach(cell => {
            cell.coordinates.forEach(coord => {
                allPoints.push(coord);
            });
        });

        // 计算凸包作为合并后的边界
        const hull = this.computeConvexHull(allPoints);

        // 稍微平滑边界
        return this.smoothPolygon(hull);
    }

    smoothPolygon(coordinates) {
        if (coordinates.length < 4) return coordinates;

        const smoothed = [];
        const smoothFactor = 0.1; // 平滑程度

        for (let i = 0; i < coordinates.length; i++) {
            const prev = coordinates[(i - 1 + coordinates.length) % coordinates.length];
            const current = coordinates[i];
            const next = coordinates[(i + 1) % coordinates.length];

            // 简单的平滑：当前点向相邻点的平均位置移动
            const smoothedX = current[0] + smoothFactor * ((prev[0] + next[0]) / 2 - current[0]);
            const smoothedY = current[1] + smoothFactor * ((prev[1] + next[1]) / 2 - current[1]);

            smoothed.push([smoothedX, smoothedY]);
        }

        // 闭合多边形
        if (smoothed.length > 0 &&
            (smoothed[0][0] !== smoothed[smoothed.length-1][0] ||
             smoothed[0][1] !== smoothed[smoothed.length-1][1])) {
            smoothed.push(smoothed[0]);
        }

        return smoothed;
    }

    computeConvexHull(points) {
        // Graham扫描算法计算凸包，添加NaN检查
        if (!points || points.length < 3) {
            console.warn('MapTalks: computeConvexHull 点数不足');
            return points || [];
        }

        // 过滤无效点
        const validPoints = points.filter(point =>
            point && point.length >= 2 &&
            !isNaN(point[0]) && !isNaN(point[1]) &&
            isFinite(point[0]) && isFinite(point[1])
        );

        if (validPoints.length < 3) {
            console.warn('MapTalks: computeConvexHull 有效点数不足');
            return validPoints;
        }

        // 找到最下方的点（y最小，如果相同则x最小）
        let start = 0;
        for (let i = 1; i < validPoints.length; i++) {
            if (validPoints[i][1] < validPoints[start][1] ||
                (validPoints[i][1] === validPoints[start][1] && validPoints[i][0] < validPoints[start][0])) {
                start = i;
            }
        }

        // 将起始点移到第一位
        [validPoints[0], validPoints[start]] = [validPoints[start], validPoints[0]];
        const startPoint = validPoints[0];

        // 按极角排序，添加NaN检查
        const sortedPoints = validPoints.slice(1).sort((a, b) => {
            const angleA = Math.atan2(a[1] - startPoint[1], a[0] - startPoint[0]);
            const angleB = Math.atan2(b[1] - startPoint[1], b[0] - startPoint[0]);

            if (isNaN(angleA) || isNaN(angleB)) {
                console.warn('MapTalks: computeConvexHull 角度计算为NaN');
                return 0;
            }

            return angleA - angleB;
        });

        // Graham扫描
        const hull = [startPoint];

        for (const point of sortedPoints) {
            // 移除不在凸包上的点
            while (hull.length > 1) {
                const crossProd = this.crossProduct(hull[hull.length-2], hull[hull.length-1], point);
                if (isNaN(crossProd)) {
                    console.warn('MapTalks: computeConvexHull 叉积计算为NaN');
                    break;
                }
                if (crossProd <= 0) {
                    hull.pop();
                } else {
                    break;
                }
            }
            hull.push(point);
        }

        return hull;
    }

    crossProduct(o, a, b) {
        return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0]);
    }

    createBufferedPolygon(hull, bufferDistance) {
        // 简化的缓冲区实现：在每个顶点周围创建圆弧
        const bufferedPoints = [];
        const segments = 8; // 每个角的分段数

        for (let i = 0; i < hull.length; i++) {
            const current = hull[i];
            const next = hull[(i + 1) % hull.length];
            const prev = hull[(i - 1 + hull.length) % hull.length];

            // 计算当前点的法向量
            const v1 = [current[0] - prev[0], current[1] - prev[1]];
            const v2 = [next[0] - current[0], next[1] - current[1]];

            // 归一化
            const len1 = Math.sqrt(v1[0] * v1[0] + v1[1] * v1[1]);
            const len2 = Math.sqrt(v2[0] * v2[0] + v2[1] * v2[1]);

            if (len1 > 0) {
                v1[0] /= len1;
                v1[1] /= len1;
            }
            if (len2 > 0) {
                v2[0] /= len2;
                v2[1] /= len2;
            }

            // 计算角平分线
            const bisector = [(v1[0] + v2[0]) / 2, (v1[1] + v2[1]) / 2];
            const bisectorLen = Math.sqrt(bisector[0] * bisector[0] + bisector[1] * bisector[1]);

            if (bisectorLen > 0) {
                bisector[0] /= bisectorLen;
                bisector[1] /= bisectorLen;

                // 添加缓冲点
                bufferedPoints.push([
                    current[0] + bisector[0] * bufferDistance,
                    current[1] + bisector[1] * bufferDistance
                ]);
            } else {
                bufferedPoints.push(current);
            }
        }

        // 闭合多边形
        if (bufferedPoints.length > 0) {
            bufferedPoints.push(bufferedPoints[0]);
        }

        return bufferedPoints;
    }

    createInterpolatedGrid(dataMap) {
        console.log('MapTalks: 创建插值网格');

        // 计算数据边界
        let minLng = Infinity, minLat = Infinity;
        let maxLng = -Infinity, maxLat = -Infinity;

        const dataPoints = [];

        this.geometryData.features.forEach(feature => {
            const cellId = feature.properties.cell_id;
            const center = this.cellCenters.get(cellId);

            if (center && dataMap[cellId]) {
                const lng = center[0];
                const lat = center[1];
                const depth = dataMap[cellId].depth;

                if (depth > 0) { // 只包含有水深的点
                    minLng = Math.min(minLng, lng);
                    minLat = Math.min(minLat, lat);
                    maxLng = Math.max(maxLng, lng);
                    maxLat = Math.max(maxLat, lat);

                    dataPoints.push({ lng, lat, depth });
                }
            }
        });

        console.log(`MapTalks: 收集到 ${dataPoints.length} 个有效数据点`);
        console.log(`MapTalks: 数据边界: [${minLng.toFixed(6)}, ${minLat.toFixed(6)}] 到 [${maxLng.toFixed(6)}, ${maxLat.toFixed(6)}]`);

        if (dataPoints.length === 0) {
            console.warn('MapTalks: 没有有效的数据点，无法创建等值面');
            return null;
        }

        // 创建规则网格
        const gridSize = 50; // 网格密度
        const stepLng = (maxLng - minLng) / gridSize;
        const stepLat = (maxLat - minLat) / gridSize;

        const grid = [];

        for (let i = 0; i <= gridSize; i++) {
            grid[i] = [];
            for (let j = 0; j <= gridSize; j++) {
                const lng = minLng + i * stepLng;
                const lat = minLat + j * stepLat;

                // 使用反距离权重插值
                const depth = this.interpolateDepth(lng, lat, dataPoints);

                grid[i][j] = {
                    lng: lng,
                    lat: lat,
                    depth: depth
                };
            }
        }

        console.log(`MapTalks: 插值网格创建完成 ${gridSize}x${gridSize}`);
        return { grid, gridSize, minLng, minLat, stepLng, stepLat };
    }

    interpolateDepth(lng, lat, dataPoints) {
        // 反距离权重插值 (IDW)
        let weightSum = 0;
        let valueSum = 0;
        const power = 2; // 距离权重指数
        const maxDistance = 0.01; // 最大影响距离

        for (const point of dataPoints) {
            const distance = Math.sqrt(
                Math.pow(lng - point.lng, 2) + Math.pow(lat - point.lat, 2)
            );

            if (distance < 0.0001) {
                // 如果距离很近，直接返回该点的值
                return point.depth;
            }

            if (distance <= maxDistance) {
                const weight = 1 / Math.pow(distance, power);
                weightSum += weight;
                valueSum += weight * point.depth;
            }
        }

        return weightSum > 0 ? valueSum / weightSum : 0;
    }

    generateContourLines(interpolatedGrid, level) {
        // 使用Marching Squares算法生成等值线
        const { grid, gridSize } = interpolatedGrid;
        const contourLines = [];

        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const cell = this.getGridCell(grid, i, j);
                const lines = this.marchingSquares(cell, level);

                lines.forEach(line => {
                    if (line.length >= 2) {
                        contourLines.push(line);
                    }
                });
            }
        }

        return contourLines;
    }

    getGridCell(grid, i, j) {
        // 获取2x2网格单元
        return {
            topLeft: grid[i][j + 1],
            topRight: grid[i + 1][j + 1],
            bottomLeft: grid[i][j],
            bottomRight: grid[i + 1][j]
        };
    }

    marchingSquares(cell, level) {
        // 简化的Marching Squares实现
        const lines = [];

        // 确定每个角点是否在等值线上方
        const tl = cell.topLeft.depth >= level ? 1 : 0;
        const tr = cell.topRight.depth >= level ? 1 : 0;
        const bl = cell.bottomLeft.depth >= level ? 1 : 0;
        const br = cell.bottomRight.depth >= level ? 1 : 0;

        // 计算配置索引
        const config = tl * 8 + tr * 4 + br * 2 + bl * 1;

        // 根据配置生成线段
        switch (config) {
            case 1: case 14:
                lines.push([
                    this.interpolatePoint(cell.bottomLeft, cell.topLeft, level),
                    this.interpolatePoint(cell.bottomLeft, cell.bottomRight, level)
                ]);
                break;
            case 2: case 13:
                lines.push([
                    this.interpolatePoint(cell.bottomLeft, cell.bottomRight, level),
                    this.interpolatePoint(cell.bottomRight, cell.topRight, level)
                ]);
                break;
            case 3: case 12:
                lines.push([
                    this.interpolatePoint(cell.bottomLeft, cell.topLeft, level),
                    this.interpolatePoint(cell.bottomRight, cell.topRight, level)
                ]);
                break;
            case 4: case 11:
                lines.push([
                    this.interpolatePoint(cell.bottomRight, cell.topRight, level),
                    this.interpolatePoint(cell.topLeft, cell.topRight, level)
                ]);
                break;
            case 6: case 9:
                lines.push([
                    this.interpolatePoint(cell.bottomLeft, cell.bottomRight, level),
                    this.interpolatePoint(cell.topLeft, cell.topRight, level)
                ]);
                break;
            case 7: case 8:
                lines.push([
                    this.interpolatePoint(cell.bottomLeft, cell.topLeft, level),
                    this.interpolatePoint(cell.topLeft, cell.topRight, level)
                ]);
                break;
        }

        return lines;
    }

    interpolatePoint(p1, p2, level) {
        // 在两点间插值找到等值点
        if (Math.abs(p1.depth - p2.depth) < 0.001) {
            return [p1.lng, p1.lat];
        }

        const ratio = (level - p1.depth) / (p2.depth - p1.depth);
        const lng = p1.lng + ratio * (p2.lng - p1.lng);
        const lat = p1.lat + ratio * (p2.lat - p1.lat);

        return [lng, lat];
    }

    createContourPolygons(interpolatedGrid, minLevel, maxLevel) {
        // 创建等值面填充区域
        const { grid, gridSize } = interpolatedGrid;
        const polygons = [];

        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const cell = this.getGridCell(grid, i, j);

                // 检查网格单元是否在指定范围内
                const depths = [
                    cell.topLeft.depth,
                    cell.topRight.depth,
                    cell.bottomLeft.depth,
                    cell.bottomRight.depth
                ];

                const minDepth = Math.min(...depths);
                const maxDepth = Math.max(...depths);

                // 如果网格单元与等值面范围有交集，创建多边形
                if (maxDepth >= minLevel && minDepth <= maxLevel) {
                    const polygon = [
                        [cell.bottomLeft.lng, cell.bottomLeft.lat],
                        [cell.bottomRight.lng, cell.bottomRight.lat],
                        [cell.topRight.lng, cell.topRight.lat],
                        [cell.topLeft.lng, cell.topLeft.lat],
                        [cell.bottomLeft.lng, cell.bottomLeft.lat]
                    ];

                    polygons.push(polygon);
                }
            }
        }

        return polygons;
    }

    updateHeatmapVisualization(dataMap, matchedCount, totalDepth, maxDepth, hasDataCount) {
        const startTime = performance.now();

        // 准备热力图数据点 - 使用缓存的中心点坐标
        const heatmapData = [];
        let validPoints = 0;
        let invalidPoints = 0;

        // 固定使用17级精细显示策略，不受缩放级别影响
        const depthThreshold = 0.05; // 固定显示所有内涝数据（≥5cm）
        const currentZoom = this.map.getZoom();

        console.log(`MapTalks: 缩放级别 ${currentZoom.toFixed(1)}, 固定17级模式 - 深度阈值 ${depthThreshold}m, 参数不变`);

        // 只遍历有数据的网格，应用双重过滤
        for (const [cellId, data] of Object.entries(dataMap)) {
            if (data.depth > 0) {
                const center = this.cellCenters.get(cellId);
                if (center && center.length === 2) {
                    const lng = center[0];
                    const lat = center[1];
                    const depth = data.depth;

                    // 严格验证坐标和数值
                    if (!isNaN(lng) && !isNaN(lat) && !isNaN(depth) &&
                        isFinite(lng) && isFinite(lat) && isFinite(depth) &&
                        lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90 && depth > 0) {

                        // 固定精细模式：显示所有有效数据
                        if (depth >= depthThreshold) {
                            // 归一化深度值到0-1范围
                            const maxReasonableDepth = 20.0;
                            const normalizedDepth = Math.min(depth / maxReasonableDepth, 1.0);

                            // 使用归一化深度作为权重，保持数据的真实性
                            heatmapData.push([lng, lat, normalizedDepth]);
                            validPoints++;
                        }
                        // 不符合深度阈值的点被过滤掉
                    } else {
                        invalidPoints++;
                        console.warn(`MapTalks: 无效数据点 ${cellId}: [${lng}, ${lat}, ${depth}]`);
                    }
                } else {
                    invalidPoints++;
                }
            }
        }

        const prepTime = performance.now() - startTime;
        console.log(`MapTalks: 热力图数据准备耗时: ${prepTime.toFixed(2)}ms, 有效点数: ${validPoints}, 无效点数: ${invalidPoints}, 最大深度: ${maxDepth.toFixed(3)}m`);

        console.log(`MapTalks: 过滤后的热力图数据点: ${heatmapData.length}`);

        if (heatmapData.length > 0) {
            try {
                const updateStartTime = performance.now();

                // 直接更新所有数据
                this.heatmapLayer.setData(heatmapData);

                // 验证热力图参数是否保持固定
                const currentOptions = this.heatmapLayer.options;
                console.log(`MapTalks: 热力图固定17级参数验证 - 半径: ${currentOptions.radius}px, 模糊: ${currentOptions.blur}px, 最小透明度: ${currentOptions.minOpacity}, 最大值: ${currentOptions.max} (原始最大深度: ${maxDepth.toFixed(3)}m)`);

                const updateTime = performance.now() - updateStartTime;
                console.log(`MapTalks: 热力图更新耗时: ${updateTime.toFixed(2)}ms`);

            } catch (error) {
                console.error('MapTalks: 热力图更新失败:', error);
                console.warn('MapTalks: 自动切换到网格模式');

                // 自动切换到网格模式
                this.changeRenderMode('grid');
                document.getElementById('renderModeSelect').value = 'grid';

                // 显示错误提示
                this.showError('热力图渲染出现问题，已自动切换到网格模式');
            }
        } else {
            // 清空热力图数据
            try {
                this.heatmapLayer.setData([]);
            } catch (error) {
                console.error('MapTalks: 清空热力图数据失败:', error);
            }
        }
    }

    updateTimeDisplay(timeStep) {
        const timeDisplay = document.getElementById('timeDisplay');
        const date = new Date(timeStep);
        timeDisplay.textContent = date.toLocaleString('zh-CN');
    }

    fitToDataBounds() {
        if (!this.polygons.length) return;

        console.log('MapTalks: 缩放到数据范围');

        // 计算所有多边形的边界
        const extent = this.floodLayer.getExtent();
        if (extent) {
            this.map.fitExtent(extent, 0, { animation: true, duration: 1000 });
        }
    }

    // 移除所有热力图参数调整方法，保持完全固定的17级效果

    // 移除所有动态调整方法，热力图参数完全固定

    adjustDataDensityByZoom(heatmapData, zoom) {
        // 根据缩放级别调整数据点密度，防止低缩放级别时过度聚合

        if (zoom >= 12) {
            // 高缩放级别：使用所有数据点
            return heatmapData;
        } else if (zoom >= 10) {
            // 中等缩放级别：使用部分数据点
            const sampleRate = 0.3 + (zoom - 10) * 0.35; // 10级:30%, 12级:100%
            return this.sampleHeatmapData(heatmapData, sampleRate);
        } else {
            // 低缩放级别：大幅减少数据点
            const sampleRate = Math.max(0.05, zoom * 0.025); // 最少5%，缩放级别越高比例越大
            return this.sampleHeatmapData(heatmapData, sampleRate);
        }
    }

    sampleHeatmapData(data, sampleRate) {
        if (sampleRate >= 1.0) return data;

        // 按深度排序，优先保留深度大的点
        const sortedData = [...data].sort((a, b) => b[2] - a[2]);

        // 计算要保留的点数
        const targetCount = Math.max(1, Math.floor(data.length * sampleRate));

        // 使用分层采样：保留高深度点，然后均匀采样其余点
        const highDepthCount = Math.floor(targetCount * 0.6); // 60%保留高深度点
        const uniformCount = targetCount - highDepthCount;     // 40%均匀采样

        const result = [];

        // 添加高深度点
        for (let i = 0; i < Math.min(highDepthCount, sortedData.length); i++) {
            result.push(sortedData[i]);
        }

        // 均匀采样剩余点
        if (uniformCount > 0 && sortedData.length > highDepthCount) {
            const remainingData = sortedData.slice(highDepthCount);
            const step = Math.max(1, Math.floor(remainingData.length / uniformCount));

            for (let i = 0; i < remainingData.length && result.length < targetCount; i += step) {
                result.push(remainingData[i]);
            }
        }

        console.log(`MapTalks: 数据采样 - 采样率: ${(sampleRate * 100).toFixed(1)}%, 原始: ${data.length}, 采样后: ${result.length}`);

        return result;
    }

    getDepthThresholdByZoom(zoom) {
        // 基于缩放级别的深度阈值：缩放级别越低，只显示越严重的内涝
        if (zoom < 9) {
            return 2.0;  // 只显示2米以上的严重内涝
        } else if (zoom < 10) {
            return 1.5;  // 显示1.5米以上的内涝
        } else if (zoom < 11) {
            return 1.0;  // 显示1米以上的内涝
        } else if (zoom < 12) {
            return 0.5;  // 显示0.5米以上的内涝
        } else if (zoom < 13) {
            return 0.2;  // 显示0.2米以上的内涝
        } else if (zoom < 14) {
            return 0.1;  // 显示0.1米以上的内涝
        } else {
            return 0.05; // 显示所有内涝（0.05米以上）
        }
    }

    calculateHeatmapWeight(originalDepth, normalizedDepth) {
        // 计算热力图权重：结合原始深度和归一化深度
        // 深度越大，权重越高，在热力图中更突出

        const baseWeight = normalizedDepth; // 基础权重基于归一化深度

        // 根据原始深度增加权重加成
        let severityMultiplier = 1.0;
        if (originalDepth >= 5.0) {
            severityMultiplier = 2.0;      // 5米以上：2倍权重
        } else if (originalDepth >= 2.0) {
            severityMultiplier = 1.5;      // 2-5米：1.5倍权重
        } else if (originalDepth >= 1.0) {
            severityMultiplier = 1.2;      // 1-2米：1.2倍权重
        }

        const finalWeight = Math.min(baseWeight * severityMultiplier, 1.0);

        return finalWeight;
    }

    // 移除地理距离计算方法，不再需要动态计算

    handlePolygonClick(polygon) {
        const props = polygon.getProperties();
        console.log(`MapTalks: 点击网格 ${props.cellId}, 水深: ${props.depth}m, 流速: ${props.velocity}m/s`);

        // 这里可以显示详细信息面板
        alert(`网格ID: ${props.cellId}\n水深: ${props.depth.toFixed(3)}m\n流速: ${props.velocity.toFixed(3)}m/s`);
    }

    setupEventListeners() {
        console.log('MapTalks: 设置事件监听...');

        // 播放/暂停按钮
        const playButton = document.getElementById('playButton');
        playButton.addEventListener('click', () => {
            this.togglePlayback();
        });

        // 时间滑块
        const timeSlider = document.getElementById('timeSlider');
        timeSlider.addEventListener('input', (e) => {
            const timeIndex = parseInt(e.target.value);
            this.updateVisualization(timeIndex);
        });

        // 透明度控制
        const opacitySlider = document.getElementById('opacitySlider');
        const opacityValue = document.getElementById('opacityValue');
        opacitySlider.addEventListener('input', (e) => {
            const opacity = e.target.value / 100;
            opacityValue.textContent = e.target.value + '%';

            // 更新所有多边形的透明度
            this.polygons.forEach(polygon => {
                const currentSymbol = polygon.getSymbol();
                polygon.updateSymbol({
                    polygonOpacity: currentSymbol.polygonOpacity * opacity
                });
            });
        });

        // 播放速度控制
        const speedSlider = document.getElementById('speedSlider');
        const speedValue = document.getElementById('speedValue');
        speedSlider.addEventListener('input', (e) => {
            this.playSpeed = parseFloat(e.target.value);
            speedValue.textContent = e.target.value + 'x';
        });

        // 缩放到数据范围按钮
        const fitBoundsButton = document.getElementById('fitBoundsButton');
        fitBoundsButton.addEventListener('click', () => {
            this.fitToDataBounds();
        });

        // 测试颜色按钮
        const testColorButton = document.getElementById('testColorButton');
        testColorButton.addEventListener('click', () => {
            this.testColorChange();
        });

        // 底图切换
        const basemapSelect = document.getElementById('basemapSelect');
        basemapSelect.addEventListener('change', (e) => {
            this.changeBasemap(e.target.value);
        });

        // 渲染模式切换
        const renderModeSelect = document.getElementById('renderModeSelect');
        renderModeSelect.addEventListener('change', (e) => {
            this.changeRenderMode(e.target.value);
        });
    }

    togglePlayback() {
        if (this.isPlaying) {
            this.pauseAnimation();
        } else {
            this.startAnimation();
        }
    }

    startAnimation() {
        console.log('MapTalks: 开始播放动画');
        this.isPlaying = true;
        document.getElementById('playButton').textContent = '⏸';

        const animate = () => {
            if (!this.isPlaying) {
                console.log('MapTalks: 动画已停止');
                return;
            }

            // 计算下一个时间步
            let nextIndex = this.currentTimeIndex + 1;
            if (nextIndex >= this.timeSteps.length) {
                nextIndex = 0; // 循环播放
                console.log('MapTalks: 循环播放，回到第一帧');
            }

            console.log(`MapTalks: 播放第 ${nextIndex} 帧`);
            this.updateVisualization(nextIndex);

            // 根据播放速度设置延迟
            const delay = 1000 / this.playSpeed;
            this.animationId = setTimeout(animate, delay);
        };

        // 立即开始第一帧动画
        animate();
    }

    pauseAnimation() {
        console.log('MapTalks: 暂停动画');
        this.isPlaying = false;
        document.getElementById('playButton').textContent = '▶';

        if (this.animationId) {
            clearTimeout(this.animationId);
            this.animationId = null;
        }
    }

    testColorChange() {
        console.log('MapTalks: 测试颜色变化');

        if (!this.polygons.length) {
            console.warn('MapTalks: 没有多边形可以测试');
            return;
        }

        const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff'];
        let colorIndex = 0;

        const changeColor = () => {
            const currentColor = colors[colorIndex % colors.length];
            console.log(`MapTalks: 设置颜色为 ${currentColor}`);

            // 更新所有多边形的颜色
            this.polygons.forEach(polygon => {
                polygon.updateSymbol({
                    polygonFill: currentColor
                });
            });

            colorIndex++;

            if (colorIndex < 10) { // 测试10次颜色变化
                setTimeout(changeColor, 500);
            } else {
                console.log('MapTalks: 颜色测试完成，恢复原始颜色');
                // 恢复基于数据的颜色
                this.updateVisualization(this.currentTimeIndex);
            }
        };

        changeColor();
    }

    changeBasemap(basemapType) {
        console.log(`MapTalks: 切换底图到 ${basemapType}`);

        let urlTemplate, attribution;

        switch (basemapType) {
            case 'dark':
                urlTemplate = 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png';
                attribution = '&copy; <a href="http://osm.org">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/">CARTO</a>';
                break;
            case 'gray':
                urlTemplate = 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png';
                attribution = '&copy; <a href="http://osm.org">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/">CARTO</a>';
                break;
            case 'satellite':
                urlTemplate = 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';
                attribution = '© Esri, © DigitalGlobe, © GeoEye, © Earthstar Geographics, © CNES/Airbus DS, © USDA, © USGS, © AeroGRID, © IGN, © the GIS User Community';
                break;
            case 'osm':
            default:
                urlTemplate = 'https://tile.openstreetmap.org/{z}/{x}/{y}.png';
                attribution = '© OpenStreetMap contributors';
                break;
        }

        // 更新底图
        const baseLayer = this.map.getBaseLayer();
        const options = {
            urlTemplate: urlTemplate,
            attribution: attribution
        };

        // 为CartoDB底图添加subdomains
        if (basemapType === 'dark' || basemapType === 'gray') {
            options.subdomains = ['a', 'b', 'c', 'd'];
        } else if (basemapType === 'osm') {
            options.subdomains = ['a', 'b', 'c'];
        }

        baseLayer.setOptions(options);

        console.log(`MapTalks: 底图已切换到 ${basemapType}`);
    }

    changeRenderMode(renderMode) {
        console.log(`MapTalks: 切换渲染模式到 ${renderMode}`);

        // 检查热力图是否可用
        if (renderMode === 'heatmap' && !this.heatmapLayer) {
            console.warn('MapTalks: 热力图插件未加载，切换到网格模式');
            renderMode = 'grid';
            document.getElementById('renderModeSelect').value = 'grid';
        }

        // 处理模糊模式
        if (renderMode === 'blur') {
            console.log('MapTalks: 切换到模糊模式');
            // 模糊模式使用网格数据，但应用CSS滤镜
        }

        this.renderMode = renderMode;

        // 根据渲染模式显示/隐藏图层和应用效果
        const heatmapWarning = document.getElementById('heatmapWarning');

        switch (renderMode) {
            case 'heatmap':
                if (this.heatmapLayer) {
                    this.floodLayer.hide();
                    this.contourLayer.hide();
                    this.heatmapLayer.show();
                    this.removeBlurEffect();

                    // 显示热力图警告
                    if (heatmapWarning) {
                        heatmapWarning.style.display = 'block';
                    }

                    // 不再调整参数，保持固定的17级效果
                }
                break;
            case 'contour':
                this.floodLayer.hide();
                if (this.heatmapLayer) {
                    this.heatmapLayer.hide();
                }
                this.contourLayer.show();
                this.removeBlurEffect();

                // 隐藏热力图警告
                if (heatmapWarning) {
                    heatmapWarning.style.display = 'none';
                }
                break;
            case 'blur':
                if (this.heatmapLayer) {
                    this.heatmapLayer.hide();
                }
                this.contourLayer.hide();
                this.floodLayer.show();
                this.applyBlurEffect();

                // 隐藏热力图警告
                if (heatmapWarning) {
                    heatmapWarning.style.display = 'none';
                }
                break;
            case 'grid':
            default:
                if (this.heatmapLayer) {
                    this.heatmapLayer.hide();
                }
                this.contourLayer.hide();
                this.floodLayer.show();
                this.removeBlurEffect();

                // 隐藏热力图警告
                if (heatmapWarning) {
                    heatmapWarning.style.display = 'none';
                }
                break;
        }

        // 重新渲染当前时间步
        this.updateVisualization(this.currentTimeIndex);

        console.log(`MapTalks: 渲染模式已切换到 ${renderMode}`);
    }

    applyBlurEffect() {
        console.log('MapTalks: 应用模糊效果');

        // 找到洪水图层的canvas元素并应用模糊滤镜
        setTimeout(() => {
            const mapContainer = document.getElementById('map');
            if (mapContainer) {
                const canvases = mapContainer.querySelectorAll('canvas');
                canvases.forEach(canvas => {
                    // 只对矢量图层应用模糊效果，不影响底图
                    if (canvas.parentElement &&
                        canvas.parentElement.className &&
                        canvas.parentElement.className.includes('maptalks-layer')) {
                        canvas.style.filter = 'blur(2px)';
                        canvas.style.transform = 'scale(1.1)'; // 稍微放大以覆盖模糊边缘
                    }
                });
            }
        }, 100);
    }

    removeBlurEffect() {
        console.log('MapTalks: 移除模糊效果');

        const mapContainer = document.getElementById('map');
        if (mapContainer) {
            const canvases = mapContainer.querySelectorAll('canvas');
            canvases.forEach(canvas => {
                canvas.style.filter = '';
                canvas.style.transform = '';
            });
        }
    }

    showError(message) {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.innerHTML = `
                <div style="color: #ff4444;">
                    <div>❌</div>
                    <div>${message}</div>
                </div>
            `;
            loading.style.display = 'block';

            // 3秒后自动隐藏错误提示
            setTimeout(() => {
                loading.style.display = 'none';
            }, 3000);
        } else {
            // 如果loading元素不存在，使用alert
            alert(message);
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new FloodVisualizationMapTalks();
});