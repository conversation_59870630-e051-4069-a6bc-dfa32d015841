/**
 * 基于热力图原理的等值面生成器
 * 使用Canvas 2D API生成光滑的等值面效果
 */
class ContourGenerator {
    constructor(map, options = {}) {
        this.map = map;
        this.options = {
            radius: options.radius || 15,           // 影响半径（像素）
            blur: options.blur || 10,               // 模糊半径（像素）
            maxValue: options.maxValue || 1.0,      // 最大值
            gradient: options.gradient || this.getDefaultGradient(),
            opacity: options.opacity || 0.8
        };
        
        this.canvas = null;
        this.ctx = null;
        this.imageData = null;
        this.contourLayer = null;
        
        this.initCanvas();
    }
    
    getDefaultGradient() {
        return {
            0.0: [0, 0, 0, 0],        // 透明
            0.1: [0, 102, 204, 255],  // 深蓝
            0.3: [0, 255, 0, 255],    // 绿色
            0.5: [255, 255, 0, 255],  // 黄色
            0.7: [255, 153, 0, 255],  // 橙色
            1.0: [255, 0, 0, 255]     // 红色
        };
    }
    
    initCanvas() {
        // 创建离屏Canvas用于等值面计算
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        
        // 设置Canvas大小 - 使用固定尺寸确保一致性
        this.canvas.width = 1024;  // 增大分辨率以提高质量
        this.canvas.height = 1024;
        
        console.log('ContourGenerator: Canvas初始化完成');
    }
    
    /**
     * 生成等值面数据
     * @param {Array} points - 数据点数组 [{lng, lat, value}, ...]
     * @param {Object} bounds - 地图边界 {north, south, east, west}
     * @returns {ImageData} 等值面图像数据
     */
    generateContour(points, bounds) {
        console.log(`ContourGenerator: 开始生成等值面，数据点: ${points.length}`);
        
        const width = this.canvas.width;
        const height = this.canvas.height;
        
        // 清空Canvas
        this.ctx.clearRect(0, 0, width, height);
        
        // 创建密度数组
        const densityData = new Float32Array(width * height);
        
        // 计算每个像素的密度值
        this.calculateDensity(points, bounds, densityData, width, height);
        
        // 应用模糊效果
        this.applyBlur(densityData, width, height);
        
        // 生成颜色图像
        const imageData = this.generateColorImage(densityData, width, height);
        
        console.log('ContourGenerator: 等值面生成完成');
        return imageData;
    }
    
    /**
     * 计算密度分布
     */
    calculateDensity(points, bounds, densityData, width, height) {
        // 使用固定的像素半径，确保在任何缩放级别都一致
        const radius = this.options.radius;
        const radiusSquared = radius * radius;

        // 地理坐标到像素坐标的转换比例
        const lngScale = width / (bounds.east - bounds.west);
        const latScale = height / (bounds.north - bounds.south);

        console.log(`ContourGenerator: 使用固定边界 - 经度范围: ${bounds.west.toFixed(6)} 到 ${bounds.east.toFixed(6)}, 纬度范围: ${bounds.south.toFixed(6)} 到 ${bounds.north.toFixed(6)}`);
        console.log(`ContourGenerator: 像素比例 - 经度: ${lngScale.toFixed(2)} px/度, 纬度: ${latScale.toFixed(2)} px/度`);
        
        points.forEach(point => {
            // 转换为像素坐标
            const pixelX = Math.round((point.lng - bounds.west) * lngScale);
            const pixelY = Math.round((bounds.north - point.lat) * latScale);
            
            // 在影响半径内分布密度
            for (let y = Math.max(0, pixelY - radius); y < Math.min(height, pixelY + radius); y++) {
                for (let x = Math.max(0, pixelX - radius); x < Math.min(width, pixelX + radius); x++) {
                    const dx = x - pixelX;
                    const dy = y - pixelY;
                    const distanceSquared = dx * dx + dy * dy;
                    
                    if (distanceSquared <= radiusSquared) {
                        // 使用高斯分布计算权重
                        const distance = Math.sqrt(distanceSquared);
                        const weight = Math.exp(-(distance * distance) / (2 * (radius / 3) * (radius / 3)));
                        
                        const index = y * width + x;
                        densityData[index] += point.value * weight;
                    }
                }
            }
        });
        
        // 归一化密度值
        this.normalizeDensity(densityData);
    }
    
    /**
     * 归一化密度数据
     */
    normalizeDensity(densityData) {
        let maxDensity = 0;
        for (let i = 0; i < densityData.length; i++) {
            maxDensity = Math.max(maxDensity, densityData[i]);
        }
        
        if (maxDensity > 0) {
            const scale = this.options.maxValue / maxDensity;
            for (let i = 0; i < densityData.length; i++) {
                densityData[i] *= scale;
            }
        }
    }
    
    /**
     * 应用模糊效果
     */
    applyBlur(densityData, width, height) {
        const blurRadius = this.options.blur;
        if (blurRadius <= 0) return;
        
        // 简化的盒式模糊
        const tempData = new Float32Array(densityData);
        
        // 水平模糊
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let sum = 0;
                let count = 0;
                
                for (let bx = Math.max(0, x - blurRadius); bx < Math.min(width, x + blurRadius + 1); bx++) {
                    sum += tempData[y * width + bx];
                    count++;
                }
                
                densityData[y * width + x] = sum / count;
            }
        }
        
        // 垂直模糊
        tempData.set(densityData);
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let sum = 0;
                let count = 0;
                
                for (let by = Math.max(0, y - blurRadius); by < Math.min(height, y + blurRadius + 1); by++) {
                    sum += tempData[by * width + x];
                    count++;
                }
                
                densityData[y * width + x] = sum / count;
            }
        }
    }
    
    /**
     * 生成颜色图像
     */
    generateColorImage(densityData, width, height) {
        const imageData = this.ctx.createImageData(width, height);
        const data = imageData.data;
        
        for (let i = 0; i < densityData.length; i++) {
            const density = Math.min(1.0, Math.max(0.0, densityData[i]));
            const color = this.getColorFromGradient(density);
            
            const pixelIndex = i * 4;
            data[pixelIndex] = color[0];     // R
            data[pixelIndex + 1] = color[1]; // G
            data[pixelIndex + 2] = color[2]; // B
            data[pixelIndex + 3] = color[3] * this.options.opacity; // A
        }
        
        return imageData;
    }
    
    /**
     * 从渐变中获取颜色
     */
    getColorFromGradient(value) {
        const gradient = this.options.gradient;
        const stops = Object.keys(gradient).map(Number).sort((a, b) => a - b);
        
        if (value <= stops[0]) {
            return gradient[stops[0]];
        }
        
        if (value >= stops[stops.length - 1]) {
            return gradient[stops[stops.length - 1]];
        }
        
        // 线性插值
        for (let i = 0; i < stops.length - 1; i++) {
            const stop1 = stops[i];
            const stop2 = stops[i + 1];
            
            if (value >= stop1 && value <= stop2) {
                const t = (value - stop1) / (stop2 - stop1);
                const color1 = gradient[stop1];
                const color2 = gradient[stop2];
                
                return [
                    Math.round(color1[0] + (color2[0] - color1[0]) * t),
                    Math.round(color1[1] + (color2[1] - color1[1]) * t),
                    Math.round(color1[2] + (color2[2] - color1[2]) * t),
                    Math.round(color1[3] + (color2[3] - color1[3]) * t)
                ];
            }
        }
        
        return [0, 0, 0, 0];
    }
    
    /**
     * 将等值面添加到地图
     */
    addToMap(imageData, bounds) {
        // 将ImageData转换为Canvas
        const canvas = document.createElement('canvas');
        canvas.width = imageData.width;
        canvas.height = imageData.height;
        const ctx = canvas.getContext('2d');
        ctx.putImageData(imageData, 0, 0);
        
        // 转换为DataURL
        const dataUrl = canvas.toDataURL();
        
        // 添加到MapTalks作为ImageLayer
        if (this.contourLayer) {
            this.map.removeLayer(this.contourLayer);
        }

        // 使用唯一的图层ID避免冲突
        const layerId = 'custom-contour-' + Date.now();
        this.contourLayer = new maptalks.ImageLayer(layerId, {
            url: dataUrl,
            extent: [bounds.west, bounds.south, bounds.east, bounds.north],
            opacity: this.options.opacity
        }).addTo(this.map);
        
        console.log('ContourGenerator: 等值面已添加到地图');
    }
    
    /**
     * 移除等值面图层
     */
    remove() {
        if (this.contourLayer) {
            try {
                this.map.removeLayer(this.contourLayer);
                console.log('ContourGenerator: 等值面图层已移除');
            } catch (error) {
                console.warn('ContourGenerator: 移除图层时出错:', error);
            }
            this.contourLayer = null;
        }
    }
    
    /**
     * 更新选项
     */
    updateOptions(newOptions) {
        this.options = { ...this.options, ...newOptions };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ContourGenerator;
} else if (typeof window !== 'undefined') {
    window.ContourGenerator = ContourGenerator;
}
