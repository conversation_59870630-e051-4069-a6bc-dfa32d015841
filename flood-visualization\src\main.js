/**
 * 城市内涝可视化系统 - 主程序
 * 基于MapLibre GL JS开发
 */

class FloodVisualization {
    constructor() {
        this.map = null;
        this.timeSeriesData = null;
        this.geometryData = null;
        this.currentTimeIndex = 0;
        this.isPlaying = false;
        this.playSpeed = 1.0;
        this.animationId = null;
        this.mapLoaded = false;

        // 时间步数组
        this.timeSteps = [];

        // 颜色比例尺
        this.depthColorScale = d3.scaleSequential()
            .domain([0, 1.0])
            .interpolator(d3.interpolateRgb('#0066cc', '#ff0000'));

        // 初始化
        this.init();
    }

    async init() {
        try {
            console.log('开始初始化系统...');

            // 初始化地图
            console.log('初始化地图...');
            this.initMap();

            // 加载数据
            console.log('开始加载数据...');
            await this.loadData();
            console.log('数据加载完成');

            // 设置事件监听
            console.log('设置事件监听...');
            this.setupEventListeners();

            // 隐藏加载界面
            console.log('隐藏加载界面');
            document.getElementById('loading').style.display = 'none';

            console.log('洪水可视化系统初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            console.error('错误堆栈:', error.stack);
            this.showError('系统初始化失败: ' + error.message);
        }
    }

    initMap() {
        // 初始化MapLibre地图
        this.map = new maplibregl.Map({
            container: 'map',
            style: {
                version: 8,
                sources: {
                    'osm': {
                        type: 'raster',
                        tiles: [
                            'https://tile.openstreetmap.org/{z}/{x}/{y}.png'
                        ],
                        tileSize: 256,
                        attribution: '© OpenStreetMap contributors'
                    }
                },
                layers: [
                    {
                        id: 'osm',
                        type: 'raster',
                        source: 'osm'
                    }
                ]
            },
            center: [108.8, 34.2], // 基于坐标转换结果的中心点
            zoom: 12,
            pitch: 0,
            bearing: 0
        });

        // 添加导航控件
        this.map.addControl(new maplibregl.NavigationControl(), 'top-right');

        // 地图加载完成后的处理
        this.map.on('load', () => {
            console.log('地图加载完成');
            this.mapLoaded = true;

            // 如果数据已经加载，初始化图层
            if (this.geometryData && this.timeSeriesData) {
                console.log('地图加载完成，数据已准备好，初始化图层...');
                this.initializeLayers();
                this.updateVisualization(0);
            } else {
                console.log('地图加载完成，等待数据加载...');
            }
        });

        // 点击事件处理
        this.map.on('click', 'flood-depth-layer', (e) => {
            this.handleMapClick(e);
        });

        // 鼠标悬停效果
        this.map.on('mouseenter', 'flood-depth-layer', () => {
            this.map.getCanvas().style.cursor = 'pointer';
        });

        this.map.on('mouseleave', 'flood-depth-layer', () => {
            this.map.getCanvas().style.cursor = '';
        });

        // 应用深色滤镜到底图
        this.map.on('load', () => {
            setTimeout(() => {
                const mapContainer = document.getElementById('map');
                if (mapContainer) {
                    const canvases = mapContainer.querySelectorAll('canvas');
                    canvases.forEach(canvas => {
                        // 只对底图canvas应用滤镜
                        if (canvas.className && canvas.className.includes('maplibregl-canvas')) {
                            canvas.style.filter = 'invert(1) hue-rotate(180deg) brightness(0.6) contrast(1.2)';
                        }
                    });
                }
            }, 1000);
        });
    }

    async loadData() {
        try {
            console.log('loadData: 开始加载数据...');

            // 加载时间序列数据
            console.log('loadData: 请求时间序列数据...');
            const timeResponse = await fetch('../processed_data/full_time_series.json');
            console.log('loadData: 时间序列响应状态:', timeResponse.status);

            if (!timeResponse.ok) {
                throw new Error(`时间序列数据加载失败! status: ${timeResponse.status}`);
            }

            console.log('loadData: 解析时间序列JSON...');
            this.timeSeriesData = await timeResponse.json();
            this.timeSteps = Object.keys(this.timeSeriesData).sort();

            console.log(`loadData: 加载了 ${this.timeSteps.length} 个时间步的数据`);

            // 尝试加载正确的几何数据
            console.log('loadData: 请求几何数据...');
            try {
                const geomResponse = await fetch('../processed_data/full_geometry.geojson');
                console.log('loadData: 几何数据响应状态:', geomResponse.status);

                if (geomResponse.ok) {
                    console.log('loadData: 解析几何JSON...');
                    this.geometryData = await geomResponse.json();
                    console.log(`loadData: 加载了真实几何数据: ${this.geometryData.features.length} 个网格`);
                } else {
                    throw new Error('几何数据HTTP请求失败');
                }
            } catch (geomError) {
                console.warn('loadData: 加载真实几何数据失败，使用模拟数据:', geomError.message);
                this.generateMockGeometry();
            }

            // 更新时间滑块
            console.log('loadData: 更新时间滑块...');
            const timeSlider = document.getElementById('timeSlider');
            timeSlider.max = this.timeSteps.length - 1;

            // 只有在地图加载完成后才初始化图层
            console.log('loadData: 检查地图加载状态:', this.mapLoaded);
            if (this.mapLoaded && this.geometryData && this.timeSeriesData) {
                console.log('loadData: 地图和数据都已准备好，初始化图层...');
                this.initializeLayers();
                this.updateVisualization(0);
            } else {
                console.log('loadData: 等待地图加载完成或数据准备...');
            }

            console.log('loadData: 数据加载完成');

        } catch (error) {
            console.error('loadData: 数据加载失败:', error);
            console.error('loadData: 错误堆栈:', error.stack);
            throw error;
        }
    }

    generateMockGeometry() {
        // 生成模拟的网格几何数据
        // 实际项目中应该从处理后的GeoJSON文件加载
        const features = [];
        const firstTimeStep = this.timeSteps[0];
        const firstTimeData = this.timeSeriesData[firstTimeStep];

        console.log(`生成几何数据，网格数量: ${firstTimeData.cells.length}`);

        // 为每个网格单元生成一个矩形几何
        firstTimeData.cells.forEach((cellId, index) => {
            // 改进的坐标分布算法，创建更合理的网格分布
            const gridCols = Math.ceil(Math.sqrt(firstTimeData.cells.length));
            const col = index % gridCols;
            const row = Math.floor(index / gridCols);

            // 基于网格ID生成更真实的坐标分布
            const baseX = 580000 + col * 500 + (Math.random() - 0.5) * 200; // 添加随机偏移
            const baseY = 3778000 + row * 500 + (Math.random() - 0.5) * 200;

            // 转换为经纬度（简化版本）
            const lng = 108.0 + (baseX - 500000) / 111320;
            const lat = baseY / 110540;

            // 创建矩形网格，大小根据数据量调整
            const size = Math.max(0.002, 0.01 / Math.sqrt(firstTimeData.cells.length));
            const feature = {
                type: 'Feature',
                properties: {
                    cell_id: cellId,
                    depth: firstTimeData.depths[index],
                    velocity: firstTimeData.velocities[index]
                },
                geometry: {
                    type: 'Polygon',
                    coordinates: [[
                        [lng - size, lat - size],
                        [lng + size, lat - size],
                        [lng + size, lat + size],
                        [lng - size, lat + size],
                        [lng - size, lat - size]
                    ]]
                }
            };

            features.push(feature);
        });

        this.geometryData = {
            type: 'FeatureCollection',
            features: features
        };

        console.log(`生成了 ${features.length} 个网格几何`);

        // 计算并显示边界框
        if (features.length > 0) {
            let minLng = Infinity, minLat = Infinity;
            let maxLng = -Infinity, maxLat = -Infinity;

            features.forEach(feature => {
                const coords = feature.geometry.coordinates[0];
                coords.forEach(([lng, lat]) => {
                    minLng = Math.min(minLng, lng);
                    minLat = Math.min(minLat, lat);
                    maxLng = Math.max(maxLng, lng);
                    maxLat = Math.max(maxLat, lat);
                });
            });

            console.log(`数据边界框: [${minLng.toFixed(6)}, ${minLat.toFixed(6)}, ${maxLng.toFixed(6)}, ${maxLat.toFixed(6)}]`);

            // 自动调整地图视图到数据范围
            const center = [(minLng + maxLng) / 2, (minLat + maxLat) / 2];
            this.map.setCenter(center);

            // 根据数据范围调整缩放级别
            const lngSpan = maxLng - minLng;
            const latSpan = maxLat - minLat;
            const maxSpan = Math.max(lngSpan, latSpan);
            const zoom = Math.max(8, Math.min(15, 12 - Math.log2(maxSpan * 100)));
            this.map.setZoom(zoom);
        }
    }

    initializeLayers() {
        console.log('初始化图层，几何数据特征数:', this.geometryData.features.length);

        try {
            // 使用第一个时间步的真实数据初始化
            console.log('使用第一个时间步数据初始化几何属性...');
            const firstTimeStep = this.timeSteps[0];
            const firstTimeData = this.timeSeriesData[firstTimeStep];

            // 创建第一个时间步的数据映射
            const firstDataMap = {};
            firstTimeData.cells.forEach((cellId, index) => {
                firstDataMap[cellId] = {
                    depth: firstTimeData.depths[index],
                    velocity: firstTimeData.velocities[index]
                };
            });

            // 为所有几何特征设置初始属性
            this.geometryData.features.forEach(feature => {
                const cellId = feature.properties.cell_id;
                if (firstDataMap[cellId]) {
                    feature.properties.depth = firstDataMap[cellId].depth;
                    feature.properties.velocity = firstDataMap[cellId].velocity;
                } else {
                    feature.properties.depth = 0;
                    feature.properties.velocity = 0;
                }
            });

            console.log('几何属性初始化完成');

            console.log('添加数据源...');
            // 添加洪水深度数据源
            this.map.addSource('flood-data', {
                type: 'geojson',
                data: this.geometryData
            });

            console.log('添加填充图层...');
            // 添加简化的动态颜色映射图层
            this.map.addLayer({
                id: 'flood-depth-layer',
                type: 'fill',
                source: 'flood-data',
                paint: {
                    'fill-color': [
                        'interpolate',
                        ['linear'],
                        ['get', 'depth'],
                        0, '#e0e0e0',      // 无水深：浅灰色
                        0.01, '#0066cc',   // 1cm：深蓝色
                        0.1, '#0099ff',    // 10cm：蓝色
                        0.2, '#33ccff',    // 20cm：浅蓝色
                        0.4, '#ffff00',    // 40cm：黄色
                        0.6, '#ff9900',    // 60cm：橙色
                        0.8, '#ff6600',    // 80cm：深橙色
                        1.0, '#ff0000'     // 100cm：红色
                    ],
                    'fill-opacity': 0.8,
                    'fill-outline-color': '#ffffff'
                }
            });

            console.log('添加边界线图层...');
            // 添加边界线图层
            this.map.addLayer({
                id: 'flood-outline-layer',
                type: 'line',
                source: 'flood-data',
                paint: {
                    'line-color': '#ffffff',
                    'line-width': 1,
                    'line-opacity': 0.8
                }
            });

            console.log('图层初始化完成');

            // 缩放到数据范围
            this.fitToDataBounds();

        } catch (error) {
            console.error('图层初始化失败:', error);
        }
    }

    updateVisualization(timeIndex) {
        if (!this.timeSeriesData || !this.geometryData) {
            console.warn('updateVisualization: 数据未加载完成');
            return;
        }

        if (!this.map.getSource('flood-data')) {
            console.warn('updateVisualization: 地图数据源不存在');
            return;
        }

        this.currentTimeIndex = timeIndex;
        const timeStep = this.timeSteps[timeIndex];
        const timeData = this.timeSeriesData[timeStep];

        console.log(`updateVisualization: 更新时间步 ${timeIndex}: ${timeStep}, 数据网格数: ${timeData.cells.length}`);

        // 创建网格ID到数据的映射
        const dataMap = {};
        timeData.cells.forEach((cellId, index) => {
            dataMap[cellId] = {
                depth: timeData.depths[index],
                velocity: timeData.velocities[index]
            };
        });

        // 统计匹配的网格数
        let matchedCount = 0;
        let totalDepth = 0;
        let maxDepth = 0;

        // 更新几何数据的属性
        this.geometryData.features.forEach((feature) => {
            const cellId = feature.properties.cell_id;
            if (dataMap[cellId]) {
                const depth = dataMap[cellId].depth;
                const velocity = dataMap[cellId].velocity;

                feature.properties.depth = depth;
                feature.properties.velocity = velocity;
                matchedCount++;
                totalDepth += depth;
                maxDepth = Math.max(maxDepth, depth);
            } else {
                // 如果没有数据，设为0
                feature.properties.depth = 0;
                feature.properties.velocity = 0;
            }
        });

        const avgDepth = matchedCount > 0 ? (totalDepth / matchedCount) : 0;
        console.log(`updateVisualization: 匹配网格数: ${matchedCount}/${this.geometryData.features.length}, 平均水深: ${avgDepth.toFixed(3)}m, 最大水深: ${maxDepth.toFixed(3)}m`);

        // 更新地图数据源
        try {
            this.map.getSource('flood-data').setData(this.geometryData);
            console.log('updateVisualization: 数据源更新成功');

            // 验证数据更新：检查有真实数据的网格
            const realDataFeatures = this.geometryData.features.filter(feature =>
                dataMap[feature.properties.cell_id] && dataMap[feature.properties.cell_id].depth > 0
            );

            console.log(`updateVisualization: 有真实水深数据的网格数: ${realDataFeatures.length}`);

            if (realDataFeatures.length > 0) {
                console.log('有水深的网格示例:');
                realDataFeatures.slice(0, 5).forEach((feature, index) => {
                    const cellId = feature.properties.cell_id;
                    const depth = feature.properties.depth;
                    console.log(`  ${cellId}: ${depth.toFixed(3)}m`);
                });
            }

            // 强制重新渲染
            this.map.triggerRepaint();

        } catch (error) {
            console.error('updateVisualization: 数据源更新失败:', error);
        }

        // 更新时间显示
        this.updateTimeDisplay(timeStep);

        // 更新滑块位置
        document.getElementById('timeSlider').value = timeIndex;
    }

    updateTimeDisplay(timeStep) {
        const timeDisplay = document.getElementById('timeDisplay');
        const date = new Date(timeStep);
        timeDisplay.textContent = date.toLocaleString('zh-CN');
    }

    setupEventListeners() {
        // 播放/暂停按钮
        const playButton = document.getElementById('playButton');
        playButton.addEventListener('click', () => {
            this.togglePlayback();
        });

        // 时间滑块
        const timeSlider = document.getElementById('timeSlider');
        timeSlider.addEventListener('input', (e) => {
            const timeIndex = parseInt(e.target.value);
            this.updateVisualization(timeIndex);
        });

        // 透明度控制
        const opacitySlider = document.getElementById('opacitySlider');
        const opacityValue = document.getElementById('opacityValue');
        opacitySlider.addEventListener('input', (e) => {
            const opacity = e.target.value / 100;
            opacityValue.textContent = e.target.value + '%';
            this.map.setPaintProperty('flood-depth-layer', 'fill-opacity', opacity);
        });

        // 播放速度控制
        const speedSlider = document.getElementById('speedSlider');
        const speedValue = document.getElementById('speedValue');
        speedSlider.addEventListener('input', (e) => {
            this.playSpeed = parseFloat(e.target.value);
            speedValue.textContent = e.target.value + 'x';
        });

        // 图层控制
        const depthLayerCheckbox = document.getElementById('depthLayer');
        depthLayerCheckbox.addEventListener('change', (e) => {
            const visibility = e.target.checked ? 'visible' : 'none';
            this.map.setLayoutProperty('flood-depth-layer', 'visibility', visibility);
            this.map.setLayoutProperty('flood-outline-layer', 'visibility', visibility);
        });

        const velocityLayerCheckbox = document.getElementById('velocityLayer');
        velocityLayerCheckbox.addEventListener('change', (e) => {
            // 流速矢量图层的实现（待完善）
            console.log('流速图层切换:', e.target.checked);
        });

        // 缩放到数据范围按钮
        const fitBoundsButton = document.getElementById('fitBoundsButton');
        fitBoundsButton.addEventListener('click', () => {
            this.fitToDataBounds();
        });

        // 测试颜色变化按钮
        const testColorButton = document.getElementById('testColorButton');
        testColorButton.addEventListener('click', () => {
            this.testColorChange();
        });

        // 测试数据颜色按钮
        const testDataColorButton = document.getElementById('testDataColorButton');
        testDataColorButton.addEventListener('click', () => {
            this.testDataBasedColor();
        });
    }

    togglePlayback() {
        if (this.isPlaying) {
            this.pauseAnimation();
        } else {
            this.startAnimation();
        }
    }

    startAnimation() {
        console.log('startAnimation: 开始播放动画');
        this.isPlaying = true;
        document.getElementById('playButton').textContent = '⏸';

        const animate = () => {
            if (!this.isPlaying) {
                console.log('startAnimation: 动画已停止');
                return;
            }

            // 计算下一个时间步
            let nextIndex = this.currentTimeIndex + 1;
            if (nextIndex >= this.timeSteps.length) {
                nextIndex = 0; // 循环播放
                console.log('startAnimation: 循环播放，回到第一帧');
            }

            console.log(`startAnimation: 播放第 ${nextIndex} 帧`);
            this.updateVisualization(nextIndex);

            // 根据播放速度设置延迟
            const delay = 1000 / this.playSpeed;
            this.animationId = setTimeout(animate, delay);
        };

        // 立即开始第一帧动画
        animate();
    }

    pauseAnimation() {
        this.isPlaying = false;
        document.getElementById('playButton').textContent = '▶';

        if (this.animationId) {
            clearTimeout(this.animationId);
            this.animationId = null;
        }
    }

    handleMapClick(e) {
        if (!e.features || e.features.length === 0) return;

        const feature = e.features[0];
        const properties = feature.properties;

        // 显示数据面板
        const dataPanel = document.getElementById('dataPanel');
        dataPanel.style.display = 'block';

        // 更新数据显示
        document.getElementById('cellId').textContent = properties.cell_id;
        document.getElementById('currentDepth').textContent = properties.depth.toFixed(3) + ' m';
        document.getElementById('currentVelocity').textContent = properties.velocity.toFixed(3) + ' m/s';

        // 计算最大水深（简化版本）
        let maxDepth = properties.depth;
        document.getElementById('maxDepth').textContent = maxDepth.toFixed(3) + ' m';

        console.log('点击网格:', properties.cell_id);
    }

    fitToDataBounds() {
        if (!this.geometryData || !this.geometryData.features.length) {
            console.warn('没有几何数据可以缩放');
            return;
        }

        // 计算数据边界框
        let minLng = Infinity, minLat = Infinity;
        let maxLng = -Infinity, maxLat = -Infinity;

        this.geometryData.features.forEach(feature => {
            const coords = feature.geometry.coordinates[0];
            coords.forEach(([lng, lat]) => {
                minLng = Math.min(minLng, lng);
                minLat = Math.min(minLat, lat);
                maxLng = Math.max(maxLng, lng);
                maxLat = Math.max(maxLat, lat);
            });
        });

        console.log(`缩放到边界框: [${minLng.toFixed(6)}, ${minLat.toFixed(6)}, ${maxLng.toFixed(6)}, ${maxLat.toFixed(6)}]`);

        // 缩放到边界框
        this.map.fitBounds([
            [minLng, minLat],
            [maxLng, maxLat]
        ], {
            padding: 50,
            duration: 1000
        });
    }

    testColorChange() {
        console.log('testColorChange: 开始测试颜色变化');

        if (!this.geometryData || !this.map.getSource('flood-data')) {
            console.warn('testColorChange: 数据或数据源不存在');
            return;
        }

        // 直接修改图层样式为固定颜色，测试图层是否能更新
        const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff'];
        let colorIndex = 0;

        const changeColor = () => {
            const currentColor = colors[colorIndex % colors.length];
            console.log(`testColorChange: 设置颜色为 ${currentColor}`);

            this.map.setPaintProperty('flood-depth-layer', 'fill-color', currentColor);
            colorIndex++;

            if (colorIndex < 10) { // 测试10次颜色变化
                setTimeout(changeColor, 500);
            } else {
                console.log('testColorChange: 颜色测试完成，恢复基于数据的样式');
                // 恢复基于数据的颜色映射，但使用更简单的表达式
                this.map.setPaintProperty('flood-depth-layer', 'fill-color', [
                    'case',
                    ['>', ['get', 'depth'], 0.5], '#ff0000',  // 大于0.5米：红色
                    ['>', ['get', 'depth'], 0.2], '#ffff00',  // 大于0.2米：黄色
                    ['>', ['get', 'depth'], 0.1], '#0099ff',  // 大于0.1米：蓝色
                    ['>', ['get', 'depth'], 0.05], '#33ccff', // 大于0.05米：浅蓝色
                    ['>', ['get', 'depth'], 0], '#e0e0e0',    // 大于0米：浅灰色
                    'rgba(0,0,0,0)'                           // 0米：透明
                ]);
                console.log('testColorChange: 已设置基于数据的颜色映射');
            }
        };

        changeColor();
    }

    testDataBasedColor() {
        console.log('testDataBasedColor: 测试基于数据的颜色映射');

        if (!this.geometryData || !this.map.getSource('flood-data')) {
            console.warn('testDataBasedColor: 数据或数据源不存在');
            return;
        }

        // 手动为有数据的网格设置明显的测试值
        let testCount = 0;
        this.geometryData.features.forEach(feature => {
            const cellId = feature.properties.cell_id;
            // 为特定的网格设置测试值
            if (cellId === 'J2088802D') {
                feature.properties.depth = 0.8; // 红色
                testCount++;
            } else if (cellId === 'J2088772D') {
                feature.properties.depth = 0.3; // 黄色
                testCount++;
            } else if (cellId === 'J2087512D') {
                feature.properties.depth = 0.15; // 蓝色
                testCount++;
            } else if (cellId === 'J2088312D') {
                feature.properties.depth = 0.08; // 浅蓝色
                testCount++;
            } else if (cellId === 'J2088382D') {
                feature.properties.depth = 0.02; // 浅灰色
                testCount++;
            } else {
                feature.properties.depth = 0; // 透明
            }
        });

        console.log(`testDataBasedColor: 设置了 ${testCount} 个测试网格的数据`);

        // 更新数据源
        this.map.getSource('flood-data').setData(this.geometryData);

        // 设置基于数据的颜色映射
        this.map.setPaintProperty('flood-depth-layer', 'fill-color', [
            'case',
            ['>', ['get', 'depth'], 0.5], '#ff0000',  // 大于0.5米：红色
            ['>', ['get', 'depth'], 0.2], '#ffff00',  // 大于0.2米：黄色
            ['>', ['get', 'depth'], 0.1], '#0099ff',  // 大于0.1米：蓝色
            ['>', ['get', 'depth'], 0.05], '#33ccff', // 大于0.05米：浅蓝色
            ['>', ['get', 'depth'], 0], '#e0e0e0',    // 大于0米：浅灰色
            'rgba(0,0,0,0)'                           // 0米：透明
        ]);

        console.log('testDataBasedColor: 应该能看到5个不同颜色的网格');
    }

    showError(message) {
        const loading = document.getElementById('loading');
        loading.innerHTML = `
            <div style="color: #ff4444;">
                <div>❌</div>
                <div>${message}</div>
            </div>
        `;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new FloodVisualization();
});