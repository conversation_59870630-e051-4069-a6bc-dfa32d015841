#!/usr/bin/env python3
"""
全量数据处理脚本
处理完整的CSV数据和Shapefile，生成完整的时间序列数据
"""

import pandas as pd
import json
import os
from datetime import datetime
import numpy as np

def process_full_csv_data():
    """处理完整的CSV数据"""
    print("=== 开始处理全量CSV数据 ===")

    try:
        # 读取完整的CSV数据
        print("正在读取完整的CSV文件...")
        df = pd.read_csv('cell_res.csv')
        print(f"读取了 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")

        # 数据基本统计
        print(f"\n数据统计:")
        print(f"唯一网格数: {df['cell_name'].nunique()}")
        print(f"时间步数: {df['time_series'].nunique()}")
        print(f"水深范围: {df['cell_depth'].min():.3f} - {df['cell_depth'].max():.3f} 米")
        print(f"流速范围: {df['cell_vel'].min():.3f} - {df['cell_vel'].max():.3f} 米/秒")

        # 解析时间
        df['time_series'] = pd.to_datetime(df['time_series'])
        time_steps = sorted(df['time_series'].unique())
        print(f"时间范围: {time_steps[0]} 到 {time_steps[-1]}")

        # 获取所有唯一的网格单元
        all_cells = sorted(df['cell_name'].unique())
        print(f"所有网格单元数: {len(all_cells)}")

        # 分析每个时间步的数据分布
        print(f"\n各时间步数据分布:")
        for i, time_step in enumerate(time_steps):
            time_data = df[df['time_series'] == time_step]
            has_depth = (time_data['cell_depth'] > 0).sum()
            max_depth = time_data['cell_depth'].max()
            print(f"时间步 {i:2d} ({time_step}): {len(time_data):3d} 网格, {has_depth:3d} 有水深, 最大水深: {max_depth:.3f}m")

        # 创建完整的时间序列数据结构
        print(f"\n正在创建完整的时间序列数据...")
        time_series_data = {}

        for time_step in time_steps:
            time_data = df[df['time_series'] == time_step]

            # 为每个时间步创建完整的网格数据
            cells_list = []
            depths_list = []
            velocities_list = []

            # 创建一个字典以便快速查找
            time_data_dict = {}
            for _, row in time_data.iterrows():
                time_data_dict[row['cell_name']] = {
                    'depth': row['cell_depth'],
                    'velocity': row['cell_vel']
                }

            # 为所有网格单元填充数据
            for cell_id in all_cells:
                cells_list.append(cell_id)
                if cell_id in time_data_dict:
                    depths_list.append(time_data_dict[cell_id]['depth'])
                    velocities_list.append(time_data_dict[cell_id]['velocity'])
                else:
                    # 如果某个时间步没有该网格的数据，填充0
                    depths_list.append(0.0)
                    velocities_list.append(0.0)

            time_series_data[time_step.isoformat()] = {
                'cells': cells_list,
                'depths': depths_list,
                'velocities': velocities_list
            }

        # 保存完整的时间序列数据
        os.makedirs('processed_data', exist_ok=True)

        output_file = 'processed_data/full_time_series.json'
        print(f"\n正在保存完整数据到 {output_file}...")

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(time_series_data, f, ensure_ascii=False, separators=(',', ':'))

        print(f"✓ 完整数据已保存")

        # 生成数据摘要
        summary = {
            'total_records': len(df),
            'unique_cells': len(all_cells),
            'time_steps': len(time_steps),
            'time_range': {
                'start': time_steps[0].isoformat(),
                'end': time_steps[-1].isoformat()
            },
            'depth_stats': {
                'min': float(df['cell_depth'].min()),
                'max': float(df['cell_depth'].max()),
                'mean': float(df['cell_depth'].mean()),
                'std': float(df['cell_depth'].std())
            },
            'velocity_stats': {
                'min': float(df['cell_vel'].min()),
                'max': float(df['cell_vel'].max()),
                'mean': float(df['cell_vel'].mean()),
                'std': float(df['cell_vel'].std())
            },
            'cells_with_data_by_timestep': []
        }

        # 统计每个时间步有数据的网格数
        for time_step in time_steps:
            time_data = df[df['time_series'] == time_step]
            has_depth = (time_data['cell_depth'] > 0).sum()
            summary['cells_with_data_by_timestep'].append({
                'time': time_step.isoformat(),
                'total_cells': len(time_data),
                'cells_with_depth': int(has_depth),
                'max_depth': float(time_data['cell_depth'].max())
            })

        # 保存摘要
        with open('processed_data/full_data_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        print(f"✓ 数据摘要已保存到 processed_data/full_data_summary.json")

        return True

    except Exception as e:
        print(f"处理数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_grid_matching():
    """分析网格匹配情况"""
    print("\n=== 分析网格匹配情况 ===")

    try:
        # 读取几何数据
        import geopandas as gpd
        print("正在读取几何数据...")
        gdf = gpd.read_file('grid_cells.shp')
        geom_cells = set(gdf['cell_id'].unique())
        print(f"几何数据中的网格数: {len(geom_cells)}")

        # 读取CSV数据
        print("正在读取CSV数据...")
        df = pd.read_csv('cell_res.csv')
        csv_cells = set(df['cell_name'].unique())
        print(f"CSV数据中的网格数: {len(csv_cells)}")

        # 分析匹配情况
        matched_cells = geom_cells.intersection(csv_cells)
        geom_only = geom_cells - csv_cells
        csv_only = csv_cells - geom_cells

        print(f"\n匹配分析:")
        print(f"匹配的网格数: {len(matched_cells)}")
        print(f"只在几何数据中的网格数: {len(geom_only)}")
        print(f"只在CSV数据中的网格数: {len(csv_only)}")

        if len(geom_only) > 0:
            print(f"\n只在几何数据中的网格示例 (前10个):")
            for cell in list(geom_only)[:10]:
                print(f"  {cell}")

        if len(csv_only) > 0:
            print(f"\n只在CSV数据中的网格示例 (前10个):")
            for cell in list(csv_only)[:10]:
                print(f"  {cell}")

        # 分析有水深数据的网格
        print(f"\n分析有水深数据的网格...")
        cells_with_depth = set(df[df['cell_depth'] > 0]['cell_name'].unique())
        matched_with_depth = matched_cells.intersection(cells_with_depth)

        print(f"有水深数据的网格数: {len(cells_with_depth)}")
        print(f"有水深且匹配几何的网格数: {len(matched_with_depth)}")

        # 保存匹配分析结果
        matching_analysis = {
            'geometry_cells': len(geom_cells),
            'csv_cells': len(csv_cells),
            'matched_cells': len(matched_cells),
            'geometry_only': len(geom_only),
            'csv_only': len(csv_only),
            'cells_with_depth': len(cells_with_depth),
            'matched_with_depth': len(matched_with_depth),
            'matched_cell_list': list(matched_cells),
            'matched_with_depth_list': list(matched_with_depth)
        }

        with open('processed_data/grid_matching_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(matching_analysis, f, ensure_ascii=False, indent=2)

        print(f"✓ 网格匹配分析已保存到 processed_data/grid_matching_analysis.json")

        return True

    except Exception as e:
        print(f"分析网格匹配时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_detailed_time_analysis():
    """创建详细的时间分析"""
    print("\n=== 创建详细时间分析 ===")

    try:
        # 读取CSV数据
        df = pd.read_csv('cell_res.csv')
        df['time_series'] = pd.to_datetime(df['time_series'])
        time_steps = sorted(df['time_series'].unique())

        detailed_analysis = []

        for i, time_step in enumerate(time_steps):
            time_data = df[df['time_series'] == time_step]

            # 基本统计
            total_cells = len(time_data)
            cells_with_depth = (time_data['cell_depth'] > 0).sum()
            max_depth = time_data['cell_depth'].max()
            avg_depth = time_data[time_data['cell_depth'] > 0]['cell_depth'].mean()

            # 深度分布
            depth_ranges = {
                '0.0-0.1m': ((time_data['cell_depth'] > 0) & (time_data['cell_depth'] <= 0.1)).sum(),
                '0.1-0.2m': ((time_data['cell_depth'] > 0.1) & (time_data['cell_depth'] <= 0.2)).sum(),
                '0.2-0.5m': ((time_data['cell_depth'] > 0.2) & (time_data['cell_depth'] <= 0.5)).sum(),
                '0.5-1.0m': ((time_data['cell_depth'] > 0.5) & (time_data['cell_depth'] <= 1.0)).sum(),
                '>1.0m': (time_data['cell_depth'] > 1.0).sum()
            }

            # 获取有水深的网格列表
            cells_with_depth_list = time_data[time_data['cell_depth'] > 0]['cell_name'].tolist()

            analysis_item = {
                'time_index': i,
                'time': time_step.isoformat(),
                'total_cells': int(total_cells),
                'cells_with_depth': int(cells_with_depth),
                'max_depth': float(max_depth),
                'avg_depth': float(avg_depth) if not pd.isna(avg_depth) else 0.0,
                'depth_distribution': {k: int(v) for k, v in depth_ranges.items()},
                'cells_with_depth_list': cells_with_depth_list[:20]  # 只保存前20个作为示例
            }

            detailed_analysis.append(analysis_item)

            print(f"时间步 {i:2d}: {cells_with_depth:3d}/{total_cells:3d} 网格有水深, 最大: {max_depth:.3f}m")

        # 保存详细分析
        with open('processed_data/detailed_time_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(detailed_analysis, f, ensure_ascii=False, indent=2)

        print(f"✓ 详细时间分析已保存到 processed_data/detailed_time_analysis.json")

        return True

    except Exception as e:
        print(f"创建详细时间分析时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🌊 全量数据处理器")
    print("=" * 50)

    # 处理完整的CSV数据
    if process_full_csv_data():
        print("\n✓ 全量CSV数据处理完成")
    else:
        print("\n❌ 全量CSV数据处理失败")
        exit(1)

    # 分析网格匹配情况
    if analyze_grid_matching():
        print("\n✓ 网格匹配分析完成")
    else:
        print("\n❌ 网格匹配分析失败")

    # 创建详细时间分析
    if create_detailed_time_analysis():
        print("\n✓ 详细时间分析完成")
    else:
        print("\n❌ 详细时间分析失败")

    print("\n" + "=" * 50)
    print("🎉 全量数据处理完成！")
    print("\n生成的文件:")
    print("  - processed_data/full_time_series.json (完整时间序列数据)")
    print("  - processed_data/full_data_summary.json (数据摘要)")
    print("  - processed_data/grid_matching_analysis.json (网格匹配分析)")
    print("  - processed_data/detailed_time_analysis.json (详细时间分析)")