<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>基于热力图原理的等值面测试</title>
    <script src="https://unpkg.com/maptalks@1.0.0-rc.25/dist/maptalks.min.js"></script>
    <script src="./contour-generator.js"></script>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; }
        .controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 1000;
            min-width: 200px;
        }
        .control-group {
            margin-bottom: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }
        select, button, input {
            width: 100%;
            padding: 4px;
            background: #333;
            color: white;
            border: 1px solid #555;
            border-radius: 3px;
            margin-bottom: 5px;
        }
        .slider-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .slider-group input {
            flex: 1;
        }
        .slider-group span {
            min-width: 30px;
            font-size: 11px;
        }
        #info {
            font-size: 11px;
            color: #ccc;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    
    <div class="controls">
        <div class="control-group">
            <label>渲染模式:</label>
            <select id="renderMode">
                <option value="points">数据点</option>
                <option value="contour">等值面</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>影响半径:</label>
            <div class="slider-group">
                <input type="range" id="radiusSlider" min="5" max="50" value="15">
                <span id="radiusValue">15px</span>
            </div>
        </div>
        
        <div class="control-group">
            <label>模糊程度:</label>
            <div class="slider-group">
                <input type="range" id="blurSlider" min="0" max="20" value="10">
                <span id="blurValue">10px</span>
            </div>
        </div>
        
        <div class="control-group">
            <label>透明度:</label>
            <div class="slider-group">
                <input type="range" id="opacitySlider" min="0.1" max="1.0" step="0.1" value="0.8">
                <span id="opacityValue">0.8</span>
            </div>
        </div>
        
        <div class="control-group">
            <button id="generateBtn">生成等值面</button>
            <button id="animateBtn">播放动画</button>
        </div>
        
        <div id="info">
            数据点: <span id="pointCount">0</span><br>
            缩放级别: <span id="zoomLevel">12</span><br>
            生成时间: <span id="generateTime">-</span>ms
        </div>
    </div>

    <script>
        // 初始化地图
        const map = new maptalks.Map('map', {
            center: [108.9, 34.2],
            zoom: 12,
            baseLayer: new maptalks.TileLayer('base', {
                urlTemplate: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',
                subdomains: ['a', 'b', 'c', 'd'],
                attribution: '© CartoDB'
            })
        });

        // 创建图层
        const pointLayer = new maptalks.VectorLayer('points').addTo(map);
        
        // 初始化等值面生成器
        const contourGenerator = new ContourGenerator(map, {
            radius: 15,
            blur: 10,
            opacity: 0.8
        });

        let currentData = [];
        let isAnimating = false;

        // 生成测试数据
        function generateTestData(frame = 0) {
            const data = [];
            const centerLng = 108.9;
            const centerLat = 34.2;
            const pointCount = 200;
            
            for (let i = 0; i < pointCount; i++) {
                // 创建几个聚集区域
                const clusterIndex = Math.floor(Math.random() * 3);
                const clusterOffsets = [
                    [0.02, 0.01],
                    [-0.01, 0.02], 
                    [0.01, -0.015]
                ];
                
                const offset = clusterOffsets[clusterIndex];
                const lng = centerLng + offset[0] + (Math.random() - 0.5) * 0.03;
                const lat = centerLat + offset[1] + (Math.random() - 0.5) * 0.03;
                
                // 模拟时间变化的深度值
                const baseDepth = Math.random() * 3;
                const timeEffect = Math.sin(frame * 0.1 + i * 0.01) * 0.5 + 0.5;
                const depth = baseDepth * timeEffect;
                
                data.push({
                    lng: lng,
                    lat: lat,
                    value: depth / 5.0, // 归一化到0-1
                    originalDepth: depth
                });
            }
            
            return data;
        }

        // 显示数据点
        function showDataPoints(data) {
            pointLayer.clear();
            
            const points = data.map(point => {
                const color = getColorByValue(point.value);
                return new maptalks.Circle([point.lng, point.lat], 100, {
                    symbol: {
                        polygonFill: color,
                        polygonOpacity: 0.7,
                        lineWidth: 1,
                        lineColor: '#ffffff',
                        lineOpacity: 0.5
                    },
                    properties: {
                        depth: point.originalDepth
                    }
                });
            });
            
            pointLayer.addGeometry(points);
        }

        // 根据数值获取颜色
        function getColorByValue(value) {
            if (value < 0.1) return '#0066cc';
            if (value < 0.3) return '#00ff00';
            if (value < 0.5) return '#ffff00';
            if (value < 0.7) return '#ff9900';
            return '#ff0000';
        }

        // 生成等值面
        function generateContour() {
            if (currentData.length === 0) return;
            
            const startTime = performance.now();
            
            // 获取地图边界
            const extent = map.getExtent();
            const bounds = {
                north: extent.ymax,
                south: extent.ymin,
                east: extent.xmax,
                west: extent.xmin
            };
            
            // 生成等值面
            const imageData = contourGenerator.generateContour(currentData, bounds);
            contourGenerator.addToMap(imageData, bounds);
            
            const endTime = performance.now();
            document.getElementById('generateTime').textContent = Math.round(endTime - startTime);
            
            console.log(`等值面生成完成，耗时: ${Math.round(endTime - startTime)}ms`);
        }

        // 更新显示
        function updateDisplay() {
            const mode = document.getElementById('renderMode').value;
            
            if (mode === 'points') {
                showDataPoints(currentData);
                contourGenerator.remove();
            } else {
                pointLayer.clear();
                generateContour();
            }
        }

        // 初始化数据
        function initData() {
            currentData = generateTestData();
            document.getElementById('pointCount').textContent = currentData.length;
            updateDisplay();
        }

        // 事件监听
        document.getElementById('renderMode').addEventListener('change', updateDisplay);

        document.getElementById('radiusSlider').addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            document.getElementById('radiusValue').textContent = value + 'px';
            contourGenerator.updateOptions({ radius: value });
        });

        document.getElementById('blurSlider').addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            document.getElementById('blurValue').textContent = value + 'px';
            contourGenerator.updateOptions({ blur: value });
        });

        document.getElementById('opacitySlider').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById('opacityValue').textContent = value;
            contourGenerator.updateOptions({ opacity: value });
        });

        document.getElementById('generateBtn').addEventListener('click', () => {
            if (document.getElementById('renderMode').value === 'contour') {
                generateContour();
            }
        });

        document.getElementById('animateBtn').addEventListener('click', () => {
            if (isAnimating) return;
            
            isAnimating = true;
            let frame = 0;
            const maxFrames = 20;
            
            const animate = () => {
                currentData = generateTestData(frame);
                document.getElementById('pointCount').textContent = currentData.length;
                updateDisplay();
                
                frame++;
                if (frame < maxFrames) {
                    setTimeout(animate, 500);
                } else {
                    isAnimating = false;
                    console.log('动画播放完成');
                }
            };
            
            animate();
        });

        // 缩放级别更新
        map.on('zoomend', () => {
            document.getElementById('zoomLevel').textContent = map.getZoom().toFixed(1);
        });

        // 地图加载完成后初始化
        map.on('load', () => {
            console.log('地图加载完成');
            initData();
        });
    </script>
</body>
</html>
