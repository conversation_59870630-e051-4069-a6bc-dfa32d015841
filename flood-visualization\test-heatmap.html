<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>热力图测试</title>
    <style type="text/css">
        html,body{margin:0px;height:100%;width:100%}
        .container{width:100%;height:100%}
    </style>
    <link rel="stylesheet" href="https://unpkg.com/maptalks/dist/maptalks.css">
</head>
<body>
    <div id="map" class="container"></div>

    <script type="text/javascript" src="https://unpkg.com/maptalks/dist/maptalks.min.js"></script>
    <script type="text/javascript" src="https://unpkg.com/maptalks.heatmap/dist/maptalks.heatmap.min.js"></script>
    <script>
        console.log('MapTalks版本:', maptalks.version);
        console.log('HeatLayer可用:', typeof maptalks.HeatLayer);
        console.log('MapTalks对象属性:', Object.keys(maptalks));

        var map = new maptalks.Map('map', {
            center: [108.9, 34.2],
            zoom: 12,
            baseLayer: new maptalks.TileLayer('base', {
                urlTemplate: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',
                subdomains: ['a', 'b', 'c', 'd'],
                attribution: '&copy; <a href="http://osm.org">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/">CARTO</a>'
            })
        });

        // 测试数据
        var data = [
            [108.85, 34.15, 0.8],
            [108.86, 34.16, 0.6],
            [108.87, 34.17, 0.9],
            [108.88, 34.18, 0.7],
            [108.89, 34.19, 1.0],
            [108.90, 34.20, 0.5],
            [108.91, 34.21, 0.8],
            [108.92, 34.22, 0.6],
            [108.93, 34.23, 0.9],
            [108.94, 34.24, 0.7]
        ];

        if (typeof maptalks.HeatLayer === 'function') {
            console.log('创建热力图层...');
            var heatLayer = new maptalks.HeatLayer('heat', data, {
                max: 1.0,
                blur: 15,
                radius: 25,
                gradient: {
                    0.4: 'blue',
                    0.6: 'cyan',
                    0.7: 'lime',
                    0.8: 'yellow',
                    1.0: 'red'
                }
            }).addTo(map);
            console.log('热力图层创建成功');
        } else {
            console.error('HeatLayer不可用');
        }
    </script>
</body>
</html>