#!/usr/bin/env python3
"""
测试坐标转换的简化脚本
"""

import pandas as pd
import json
import os

def simple_coordinate_transform(x, y):
    """
    简化的坐标转换函数
    从CGCS2000高斯-克吕格投影(108°E)转换为WGS84
    """
    # 这是一个近似转换，实际项目中应使用pyproj
    # 基于中央经线108°E的高斯-克吕格投影参数

    # 去除假东偏移
    x_adjusted = x - 500000.0

    # 简化的反投影计算（近似）
    # 这里使用简化公式，实际应用中需要完整的投影变换
    central_meridian = 108.0  # 中央经线

    # 近似计算经纬度
    lng = central_meridian + (x_adjusted / 111320.0)  # 近似每度经度的米数
    lat = y / 110540.0  # 近似每度纬度的米数

    return lng, lat

def process_sample_data():
    """处理样本数据"""
    print("开始处理样本数据...")

    # 读取CSV数据的前1000行进行测试
    try:
        df = pd.read_csv('cell_res.csv', nrows=1000)
        print(f"读取了 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")

        # 获取唯一的网格单元
        unique_cells = df['cell_name'].unique()
        print(f"唯一网格单元数: {len(unique_cells)}")

        # 解析时间
        df['time_series'] = pd.to_datetime(df['time_series'])
        time_steps = sorted(df['time_series'].unique())
        print(f"时间步数: {len(time_steps)}")
        print(f"时间范围: {time_steps[0]} 到 {time_steps[-1]}")

        # 统计数据范围
        print(f"\n数据统计:")
        print(f"水深范围: {df['cell_depth'].min():.3f} - {df['cell_depth'].max():.3f} 米")
        print(f"流速范围: {df['cell_vel'].min():.3f} - {df['cell_vel'].max():.3f} 米/秒")

        # 创建时间序列数据结构 - 修复版本
        time_series_data = {}

        # 获取所有唯一的网格单元
        all_cells = sorted(unique_cells)

        for time_step in time_steps:
            time_data = df[df['time_series'] == time_step]

            # 为每个时间步创建完整的网格数据
            cells_list = []
            depths_list = []
            velocities_list = []

            # 创建一个字典以便快速查找
            time_data_dict = {}
            for _, row in time_data.iterrows():
                time_data_dict[row['cell_name']] = {
                    'depth': row['cell_depth'],
                    'velocity': row['cell_vel']
                }

            # 为所有网格单元填充数据
            for cell_id in all_cells:
                cells_list.append(cell_id)
                if cell_id in time_data_dict:
                    depths_list.append(time_data_dict[cell_id]['depth'])
                    velocities_list.append(time_data_dict[cell_id]['velocity'])
                else:
                    # 如果某个时间步没有该网格的数据，填充0
                    depths_list.append(0.0)
                    velocities_list.append(0.0)

            time_series_data[time_step.isoformat()] = {
                'cells': cells_list,
                'depths': depths_list,
                'velocities': velocities_list
            }

            print(f"时间步 {time_step}: {len(cells_list)} 个网格单元")

        # 保存处理后的数据
        os.makedirs('processed_data', exist_ok=True)

        with open('processed_data/sample_time_series.json', 'w', encoding='utf-8') as f:
            json.dump(time_series_data, f, ensure_ascii=False, indent=2)

        print(f"\n样本数据已保存到 processed_data/sample_time_series.json")

        return True

    except Exception as e:
        print(f"处理数据时出错: {e}")
        return False

def test_coordinate_conversion():
    """测试坐标转换"""
    print("\n测试坐标转换...")

    # 测试一些示例坐标（基于数据可能的范围）
    test_coords = [
        (580000, 3778000),  # 示例坐标
        (590000, 3780000),
        (600000, 3785000)
    ]

    print("原始坐标 -> WGS84坐标")
    for x, y in test_coords:
        lng, lat = simple_coordinate_transform(x, y)
        print(f"({x}, {y}) -> ({lng:.6f}, {lat:.6f})")

        # 验证坐标是否在合理范围内（中国境内）
        if 70 <= lng <= 140 and 15 <= lat <= 55:
            print("  ✓ 坐标在合理范围内")
        else:
            print("  ✗ 坐标超出合理范围")

if __name__ == "__main__":
    print("=== 城市内涝数据预处理测试 ===")

    # 检查文件是否存在
    if not os.path.exists('cell_res.csv'):
        print("错误：找不到 cell_res.csv 文件")
        exit(1)

    # 处理样本数据
    if process_sample_data():
        print("✓ 样本数据处理成功")
    else:
        print("✗ 样本数据处理失败")

    # 测试坐标转换
    test_coordinate_conversion()

    print("\n=== 测试完成 ===")