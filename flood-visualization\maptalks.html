<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市内涝可视化 - MapTalks版本</title>
    <link rel="stylesheet" href="https://unpkg.com/maptalks@1.0.0-rc.25/dist/maptalks.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }

        #map {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .control-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 20px;
            min-width: 300px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
        }

        .control-panel h2 {
            margin-bottom: 15px;
            color: #4CAF50;
            font-size: 18px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #cccccc;
        }

        .control-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }

        .control-button:hover {
            background: #45a049;
        }

        .timeline-control {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 15px 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            min-width: 600px;
            z-index: 1000;
        }

        .play-button {
            background: #4CAF50;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .play-button:hover {
            background: #45a049;
        }

        .time-slider {
            flex: 1;
            height: 6px;
            background: #333;
            border-radius: 3px;
            outline: none;
            cursor: pointer;
        }

        .time-display {
            font-size: 14px;
            color: #cccccc;
            min-width: 120px;
            text-align: center;
        }

        .legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
        }

        .legend h3 {
            margin-bottom: 10px;
            color: #4CAF50;
            font-size: 16px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .legend-color {
            width: 20px;
            height: 15px;
            margin-right: 8px;
            border-radius: 2px;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            z-index: 2000;
        }

        .spinner {
            border: 3px solid #333;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="map"></div>

    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div>正在加载数据...</div>
    </div>

    <div class="control-panel">
        <h2>🌊 内涝可视化控制</h2>

        <div class="control-group">
            <button class="control-button" id="fitBoundsButton">缩放到数据范围</button>
            <button class="control-button" id="testColorButton">测试颜色</button>
        </div>

        <div class="control-group">
            <label for="basemapSelect">底图样式:</label>
            <select id="basemapSelect" style="width: 100%; padding: 4px; background: #333; color: white; border: 1px solid #555;">
                <option value="dark">深色底图</option>
                <option value="gray">灰色底图</option>
                <option value="satellite">卫星底图</option>
                <option value="osm">标准底图</option>
            </select>
        </div>

        <div class="control-group">
            <label>
                <input type="checkbox" id="depthLayer" checked> 显示水深
            </label>
        </div>

        <div class="control-group">
            <label for="renderModeSelect">渲染模式:</label>
            <select id="renderModeSelect" style="width: 100%; padding: 4px; background: #333; color: white; border: 1px solid #555;">
                <option value="grid">网格模式</option>
                <option value="blur">模糊模式</option>
                <option value="contour">等值面模式</option>
                <option value="heatmap">热力图模式</option>
            </select>
            <div id="heatmapWarning" style="display: none; margin-top: 5px; padding: 5px; background: rgba(255, 165, 0, 0.2); border-radius: 3px; font-size: 11px; color: #ffa500;">
                ⚠️ 热力图模式：放大查看时数值仅供参考，实际水深以网格模式为准
            </div>
        </div>

        <div class="control-group">
            <label for="opacitySlider">透明度: <span id="opacityValue">80%</span></label>
            <input type="range" id="opacitySlider" min="0" max="100" value="80" class="time-slider">
        </div>

        <div class="control-group">
            <label for="speedSlider">播放速度: <span id="speedValue">1x</span></label>
            <input type="range" id="speedSlider" min="0.5" max="3" step="0.5" value="1" class="time-slider">
        </div>
    </div>

    <div class="timeline-control">
        <button class="play-button" id="playButton">▶</button>
        <input type="range" id="timeSlider" min="0" max="17" value="0" class="time-slider">
        <div class="time-display" id="timeDisplay">2025-04-30 00:30:00</div>
    </div>

    <div class="legend">
        <h3>水深图例 (米)</h3>
        <div class="legend-item">
            <div class="legend-color" style="background: #87CEEB;"></div>
            <span>0.0 - 0.1</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #4169E1;"></div>
            <span>0.1 - 0.3</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #0000FF;"></div>
            <span>0.3 - 0.5</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #00FFFF;"></div>
            <span>0.5 - 1.0</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #00FF00;"></div>
            <span>1.0 - 2.0</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #FFFF00;"></div>
            <span>2.0 - 5.0</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #FFA500;"></div>
            <span>5.0 - 10.0</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #FF4500;"></div>
            <span>10.0 - 15.0</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #FF0000;"></div>
            <span>> 15.0</span>
        </div>
    </div>

    <script src="https://unpkg.com/maptalks@1.0.0-rc.25/dist/maptalks.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/maptalks.heatmap@latest/dist/maptalks.heatmap.min.js"></script>
    <script>
        // 检查插件加载状态
        console.log('MapTalks版本:', maptalks.version);
        console.log('HeatLayer可用:', typeof maptalks.HeatLayer);
        console.log('MapTalks对象属性:', Object.keys(maptalks));
    </script>
    <script src="./maptalks-flood.js"></script>
</body>
</html>