#!/usr/bin/env python3
"""
正确的坐标转换测试脚本
使用真实的Shapefile坐标进行转换验证
"""

import geopandas as gpd
import pandas as pd
import json
import os
from pyproj import Transformer

def test_coordinate_conversion():
    """测试坐标转换的准确性"""
    print("=== 坐标转换验证 ===")

    # 创建正确的坐标转换器：EPSG:4545 -> WGS84
    transformer = Transformer.from_crs(4545, 4326, always_xy=True)

    # 读取Shapefile获取真实坐标
    try:
        gdf = gpd.read_file('2D_CELL.shp')
        print(f"读取Shapefile成功，包含 {len(gdf)} 个几何对象")

        # 获取边界框
        bounds = gdf.total_bounds
        print(f"原始坐标边界框: [{bounds[0]:.2f}, {bounds[1]:.2f}, {bounds[2]:.2f}, {bounds[3]:.2f}]")

        # 转换边界框坐标
        min_lng, min_lat = transformer.transform(bounds[0], bounds[1])
        max_lng, max_lat = transformer.transform(bounds[2], bounds[3])

        print(f"转换后边界框: [{min_lng:.6f}, {min_lat:.6f}, {max_lng:.6f}, {max_lat:.6f}]")

        # 测试几个样本点
        print("\n样本坐标转换:")
        for i in range(min(5, len(gdf))):
            geom = gdf.iloc[i].geometry
            centroid = geom.centroid
            lng, lat = transformer.transform(centroid.x, centroid.y)
            junname = gdf.iloc[i]['JUNNAME']
            print(f"{junname}: ({centroid.x:.2f}, {centroid.y:.2f}) -> ({lng:.6f}, {lat:.6f})")

        return gdf, transformer

    except Exception as e:
        print(f"读取Shapefile失败: {e}")
        return None, None

def create_correct_geometry_data():
    """创建基于真实Shapefile的几何数据"""
    print("\n=== 创建正确的几何数据 ===")

    gdf, transformer = test_coordinate_conversion()
    if gdf is None:
        return None

    # 读取时间序列数据
    try:
        df = pd.read_csv('cell_res.csv', nrows=5000)  # 读取更多数据用于测试
        print(f"读取CSV数据: {len(df)} 行")

        # 获取有数据的网格单元
        available_cells = set(df['cell_name'].unique())
        print(f"CSV中的网格单元数: {len(available_cells)}")

        # 筛选Shapefile中存在于CSV数据中的几何
        gdf_filtered = gdf[gdf['JUNNAME'].isin(available_cells)].copy()
        print(f"匹配的几何数量: {len(gdf_filtered)}")

        # 转换几何坐标
        features = []
        for idx, row in gdf_filtered.iterrows():
            geom = row.geometry
            junname = row['JUNNAME']

            if geom.geom_type == 'Polygon':
                # 转换多边形坐标
                exterior_coords = []
                for x, y in geom.exterior.coords:
                    lng, lat = transformer.transform(x, y)
                    exterior_coords.append([lng, lat])

                feature = {
                    "type": "Feature",
                    "properties": {
                        "cell_id": junname,
                        "original_x": geom.centroid.x,
                        "original_y": geom.centroid.y
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [exterior_coords]
                    }
                }
                features.append(feature)

        # 保存转换后的几何数据
        geometry_data = {
            "type": "FeatureCollection",
            "features": features
        }

        os.makedirs('processed_data', exist_ok=True)
        with open('processed_data/correct_geometry.geojson', 'w', encoding='utf-8') as f:
            json.dump(geometry_data, f, ensure_ascii=False, indent=2)

        print(f"保存了 {len(features)} 个转换后的几何对象")

        # 计算转换后的边界框
        if features:
            all_coords = []
            for feature in features:
                all_coords.extend(feature['geometry']['coordinates'][0])

            lngs, lats = zip(*all_coords)
            bounds = [min(lngs), min(lats), max(lngs), max(lats)]
            print(f"转换后数据边界框: {bounds}")

        return geometry_data

    except Exception as e:
        print(f"处理数据失败: {e}")
        return None

if __name__ == "__main__":
    create_correct_geometry_data()