<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市内涝模拟可视化系统</title>
    <link href="https://unpkg.com/maplibre-gl@4.0.0/dist/maplibre-gl.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }

        #map {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .control-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 20px;
            min-width: 300px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-panel h2 {
            margin-bottom: 15px;
            color: #4CAF50;
            font-size: 18px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #cccccc;
        }

        .timeline-control {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 15px 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            min-width: 600px;
        }

        .play-button {
            background: #4CAF50;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .play-button:hover {
            background: #45a049;
        }

        .time-slider {
            flex: 1;
            height: 6px;
            background: #333;
            border-radius: 3px;
            outline: none;
            cursor: pointer;
        }

        .time-display {
            font-size: 14px;
            color: #cccccc;
            min-width: 120px;
            text-align: center;
        }

        .legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .legend h3 {
            margin-bottom: 10px;
            color: #4CAF50;
            font-size: 16px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .legend-color {
            width: 20px;
            height: 15px;
            margin-right: 8px;
            border-radius: 2px;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            z-index: 1000;
        }

        .spinner {
            border: 3px solid #333;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .data-panel {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-width: 200px;
            display: none;
        }

        .data-panel h3 {
            margin-bottom: 10px;
            color: #4CAF50;
            font-size: 16px;
        }

        .data-item {
            margin-bottom: 8px;
            font-size: 14px;
        }

        .data-label {
            color: #cccccc;
        }

        .data-value {
            color: #ffffff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="map"></div>

    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div>正在加载数据...</div>
    </div>

    <div class="control-panel">
        <h2>🌊 内涝可视化控制</h2>

        <div class="control-group">
            <label>
                <input type="checkbox" id="depthLayer" checked> 显示水深
            </label>
        </div>

        <div class="control-group">
            <label>
                <input type="checkbox" id="velocityLayer"> 显示流速矢量
            </label>
        </div>

        <div class="control-group">
            <label for="opacitySlider">透明度: <span id="opacityValue">80%</span></label>
            <input type="range" id="opacitySlider" min="0" max="100" value="80" class="time-slider">
        </div>

        <div class="control-group">
            <label for="speedSlider">播放速度: <span id="speedValue">1x</span></label>
            <input type="range" id="speedSlider" min="0.5" max="3" step="0.5" value="1" class="time-slider">
        </div>

        <div class="control-group">
            <button id="fitBoundsButton" style="background: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                缩放到数据范围
            </button>
        </div>

        <div class="control-group">
            <button id="testColorButton" style="background: #ff6600; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                测试颜色变化
            </button>
        </div>

        <div class="control-group">
            <button id="testDataColorButton" style="background: #9c27b0; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                测试数据颜色
            </button>
        </div>
    </div>

    <div class="timeline-control">
        <button class="play-button" id="playButton">▶</button>
        <input type="range" id="timeSlider" min="0" max="17" value="0" class="time-slider">
        <div class="time-display" id="timeDisplay">2025-04-30 00:30:00</div>
    </div>

    <div class="legend">
        <h3>水深图例 (米)</h3>
        <div class="legend-item">
            <div class="legend-color" style="background: #0066cc;"></div>
            <span>0.0 - 0.2</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #0099ff;"></div>
            <span>0.2 - 0.4</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #33ccff;"></div>
            <span>0.4 - 0.6</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #ffff00;"></div>
            <span>0.6 - 0.8</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #ff9900;"></div>
            <span>0.8 - 1.0</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #ff0000;"></div>
            <span>> 1.0</span>
        </div>
    </div>

    <div class="data-panel" id="dataPanel">
        <h3>网格信息</h3>
        <div class="data-item">
            <span class="data-label">网格ID:</span>
            <span class="data-value" id="cellId">-</span>
        </div>
        <div class="data-item">
            <span class="data-label">当前水深:</span>
            <span class="data-value" id="currentDepth">-</span>
        </div>
        <div class="data-item">
            <span class="data-label">当前流速:</span>
            <span class="data-value" id="currentVelocity">-</span>
        </div>
        <div class="data-item">
            <span class="data-label">最大水深:</span>
            <span class="data-value" id="maxDepth">-</span>
        </div>
    </div>

    <script src="https://unpkg.com/maplibre-gl@4.0.0/dist/maplibre-gl.js"></script>
    <script src="https://unpkg.com/d3-scale@4.0.2/dist/d3-scale.min.js"></script>
    <script src="https://unpkg.com/d3-interpolate@3.0.1/dist/d3-interpolate.min.js"></script>
    <script src="https://unpkg.com/d3-color@3.1.0/dist/d3-color.min.js"></script>
    <script src="./src/main.js"></script>
</body>
</html>