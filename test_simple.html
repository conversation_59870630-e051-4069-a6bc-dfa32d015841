<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化测试页面</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #status { background: #f0f0f0; padding: 10px; margin-bottom: 20px; }
        #error { color: red; }
        #success { color: green; }
    </style>
</head>
<body>
    <h1>数据加载测试</h1>
    <div id="status">正在测试...</div>

    <script>
        async function testDataLoading() {
            const statusDiv = document.getElementById('status');

            try {
                statusDiv.innerHTML = '<div>测试1: 加载时间序列数据...</div>';

                // 测试时间序列数据
                const timeResponse = await fetch('./processed_data/sample_time_series.json');
                console.log('时间序列响应:', timeResponse);

                if (!timeResponse.ok) {
                    throw new Error(`时间序列数据加载失败: ${timeResponse.status}`);
                }

                const timeData = await timeResponse.json();
                const timeSteps = Object.keys(timeData);

                statusDiv.innerHTML += '<div id="success">✓ 时间序列数据加载成功: ' + timeSteps.length + ' 个时间步</div>';

                // 测试几何数据
                statusDiv.innerHTML += '<div>测试2: 加载几何数据...</div>';

                const geomResponse = await fetch('./processed_data/correct_geometry.geojson');
                console.log('几何数据响应:', geomResponse);

                if (!geomResponse.ok) {
                    throw new Error(`几何数据加载失败: ${geomResponse.status}`);
                }

                const geomData = await geomResponse.json();

                statusDiv.innerHTML += '<div id="success">✓ 几何数据加载成功: ' + geomData.features.length + ' 个网格</div>';

                // 测试数据匹配
                statusDiv.innerHTML += '<div>测试3: 检查数据匹配...</div>';

                const firstTimeStep = timeSteps[0];
                const firstTimeData = timeData[firstTimeStep];

                let matchCount = 0;
                const timeDataCells = new Set(firstTimeData.cells);

                geomData.features.forEach(feature => {
                    if (timeDataCells.has(feature.properties.cell_id)) {
                        matchCount++;
                    }
                });

                statusDiv.innerHTML += '<div id="success">✓ 数据匹配检查完成: ' + matchCount + '/' + geomData.features.length + ' 个网格有时间序列数据</div>';

                statusDiv.innerHTML += '<div id="success"><strong>所有测试通过！数据加载正常。</strong></div>';

            } catch (error) {
                console.error('测试失败:', error);
                statusDiv.innerHTML += '<div id="error">❌ 测试失败: ' + error.message + '</div>';
            }
        }

        // 页面加载后开始测试
        document.addEventListener('DOMContentLoaded', testDataLoading);
    </script>
</body>
</html>