#!/usr/bin/env python3
"""
城市内涝数据预处理脚本
处理Shapefile和CSV数据，实现坐标系转换，生成优化的数据格式
"""

import geopandas as gpd
import pandas as pd
import json
import numpy as np
from pyproj import Transformer
from shapely.geometry import mapping
import os
from datetime import datetime

class FloodDataProcessor:
    def __init__(self, shapefile_path, csv_path, output_dir="processed_data"):
        self.shapefile_path = shapefile_path
        self.csv_path = csv_path
        self.output_dir = output_dir

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 初始化坐标转换器：CGCS2000高斯-克吕格(108°E) -> WGS84
        # 根据.prj文件内容，这是CGCS2000_3_degree_Gauss_Kruger_CM_108E
        self.transformer = Transformer.from_proj(
            "+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs",
            "+proj=longlat +datum=WGS84 +no_defs",
            always_xy=True
        )

    def load_data(self):
        """加载原始数据"""
        print("正在加载数据...")

        # 加载Shapefile
        self.gdf = gpd.read_file(self.shapefile_path)
        print(f"加载了 {len(self.gdf)} 个网格单元")
        print(f"Shapefile列: {list(self.gdf.columns)}")

        # 加载CSV数据
        self.df = pd.read_csv(self.csv_path)
        print(f"加载了 {len(self.df)} 条时间序列记录")
        print(f"CSV列: {list(self.df.columns)}")

        # 解析时间
        self.df['time_series'] = pd.to_datetime(self.df['time_series'])

        return self

    def transform_coordinates(self):
        """转换坐标系"""
        print("正在转换坐标系...")

        # 转换几何坐标
        def transform_geom(geom):
            if geom.geom_type == 'Polygon':
                exterior_coords = list(geom.exterior.coords)
                transformed_coords = []

                for x, y in exterior_coords:
                    try:
                        lng, lat = self.transformer.transform(x, y)
                        # 验证坐标是否合理（中国境内）
                        if 70 <= lng <= 140 and 15 <= lat <= 55:
                            transformed_coords.append([lng, lat])
                        else:
                            print(f"警告：坐标超出合理范围 ({lng}, {lat})")
                    except Exception as e:
                        print(f"坐标转换失败: {x}, {y} - {e}")

                if len(transformed_coords) >= 3:  # 至少需要3个点形成多边形
                    return {
                        "type": "Polygon",
                        "coordinates": [transformed_coords]
                    }
            return None

        # 应用坐标转换
        self.gdf['geometry_wgs84'] = self.gdf['geometry'].apply(transform_geom)

        # 过滤掉转换失败的几何
        valid_geoms = self.gdf['geometry_wgs84'].notna()
        print(f"成功转换 {valid_geoms.sum()} / {len(self.gdf)} 个几何对象")

        # 计算边界框
        all_coords = []
        for geom in self.gdf[valid_geoms]['geometry_wgs84']:
            if geom:
                all_coords.extend(geom['coordinates'][0])

        if all_coords:
            lngs, lats = zip(*all_coords)
            self.bounds = [min(lngs), min(lats), max(lngs), max(lats)]
            print(f"数据边界框: {self.bounds}")

        return self

    def process_time_series(self):
        """处理时间序列数据"""
        print("正在处理时间序列数据...")

        # 获取所有时间步
        self.time_steps = sorted(self.df['time_series'].unique())
        print(f"时间步数: {len(self.time_steps)}")

        # 按网格单元组织数据
        self.cell_data = {}

        for cell_name in self.df['cell_name'].unique():
            cell_records = self.df[self.df['cell_name'] == cell_name].sort_values('time_series')

            self.cell_data[cell_name] = {
                'depths': cell_records['cell_depth'].tolist(),
                'velocities': cell_records['cell_vel'].tolist(),
                'times': [t.isoformat() for t in cell_records['time_series']]
            }

        print(f"处理了 {len(self.cell_data)} 个网格单元的时间序列")
        return self

    def merge_spatial_temporal(self):
        """合并空间和时间数据"""
        print("正在合并空间和时间数据...")

        # 创建合并后的数据结构
        self.merged_features = []

        for idx, row in self.gdf.iterrows():
            if pd.isna(row['geometry_wgs84']):
                continue

            cell_name = row['JUNNAME']  # 根据Shapefile的字段名

            if cell_name in self.cell_data:
                feature = {
                    "type": "Feature",
                    "properties": {
                        "cell_id": cell_name,
                        "elevation": row.get('ELEVATION', 0),
                        "area": row.get('AREA', 0),
                        "depths": self.cell_data[cell_name]['depths'],
                        "velocities": self.cell_data[cell_name]['velocities'],
                        "max_depth": max(self.cell_data[cell_name]['depths']),
                        "max_velocity": max(self.cell_data[cell_name]['velocities'])
                    },
                    "geometry": row['geometry_wgs84']
                }
                self.merged_features.append(feature)

        print(f"成功合并 {len(self.merged_features)} 个要素")
        return self

    def generate_optimized_data(self):
        """生成优化的数据格式"""
        print("正在生成优化数据格式...")

        # 1. 生成完整的GeoJSON
        full_geojson = {
            "type": "FeatureCollection",
            "features": self.merged_features,
            "metadata": {
                "bounds": self.bounds,
                "time_steps": [t.isoformat() for t in self.time_steps],
                "total_features": len(self.merged_features),
                "generated_at": datetime.now().isoformat()
            }
        }

        # 保存完整GeoJSON
        with open(os.path.join(self.output_dir, "flood_data_full.geojson"), 'w', encoding='utf-8') as f:
            json.dump(full_geojson, f, ensure_ascii=False, separators=(',', ':'))

        # 2. 生成分时间步的数据
        time_step_data = {}
        for i, time_step in enumerate(self.time_steps):
            time_data = []
            for feature in self.merged_features:
                props = feature['properties']
                time_data.append({
                    "cell_id": props['cell_id'],
                    "depth": props['depths'][i] if i < len(props['depths']) else 0,
                    "velocity": props['velocities'][i] if i < len(props['velocities']) else 0
                })

            time_step_data[time_step.isoformat()] = time_data

        # 保存时间步数据
        with open(os.path.join(self.output_dir, "time_series_data.json"), 'w', encoding='utf-8') as f:
            json.dump(time_step_data, f, ensure_ascii=False, separators=(',', ':'))

        # 3. 生成几何数据（不包含时间序列）
        geometry_features = []
        for feature in self.merged_features:
            geom_feature = {
                "type": "Feature",
                "properties": {
                    "cell_id": feature['properties']['cell_id'],
                    "elevation": feature['properties']['elevation'],
                    "area": feature['properties']['area'],
                    "max_depth": feature['properties']['max_depth'],
                    "max_velocity": feature['properties']['max_velocity']
                },
                "geometry": feature['geometry']
            }
            geometry_features.append(geom_feature)

        geometry_geojson = {
            "type": "FeatureCollection",
            "features": geometry_features
        }

        # 保存几何数据
        with open(os.path.join(self.output_dir, "flood_geometry.geojson"), 'w', encoding='utf-8') as f:
            json.dump(geometry_geojson, f, ensure_ascii=False, separators=(',', ':'))

        print("数据生成完成！")
        return self

    def process_all(self):
        """执行完整的数据处理流程"""
        return (self.load_data()
                   .transform_coordinates()
                   .process_time_series()
                   .merge_spatial_temporal()
                   .generate_optimized_data())

def main():
    """主函数"""
    # 配置文件路径
    shapefile_path = "2D_CELL.shp"
    csv_path = "cell_res.csv"
    output_dir = "processed_data"

    # 检查文件是否存在
    if not os.path.exists(shapefile_path):
        print(f"错误：找不到Shapefile: {shapefile_path}")
        return

    if not os.path.exists(csv_path):
        print(f"错误：找不到CSV文件: {csv_path}")
        return

    # 执行数据处理
    try:
        processor = FloodDataProcessor(shapefile_path, csv_path, output_dir)
        processor.process_all()

        print("\n=== 处理完成 ===")
        print(f"输出目录: {output_dir}")
        print("生成的文件:")
        print("- flood_data_full.geojson: 完整数据（包含几何和时间序列）")
        print("- flood_geometry.geojson: 几何数据（用于地图渲染）")
        print("- time_series_data.json: 时间序列数据（用于动画）")

        # 显示统计信息
        if hasattr(processor, 'bounds'):
            print(f"\n数据边界框: {processor.bounds}")
        if hasattr(processor, 'time_steps'):
            print(f"时间范围: {processor.time_steps[0]} 到 {processor.time_steps[-1]}")
            print(f"时间步数: {len(processor.time_steps)}")
        if hasattr(processor, 'merged_features'):
            print(f"有效网格单元数: {len(processor.merged_features)}")

    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()