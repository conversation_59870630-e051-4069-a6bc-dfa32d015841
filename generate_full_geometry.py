#!/usr/bin/env python3
"""
生成完整的几何数据
从2D_CELL.shp中提取CSV数据中包含的所有网格
"""

import geopandas as gpd
import pandas as pd
import json
import os

def generate_full_geometry():
    """生成完整的几何数据"""
    print("=== 生成完整几何数据 ===")

    try:
        # 读取CSV数据获取所有网格ID
        print("正在读取CSV数据...")
        df = pd.read_csv('cell_res.csv')
        csv_cells = set(df['cell_name'].unique())
        print(f"CSV数据中的网格数: {len(csv_cells)}")

        # 读取完整的Shapefile
        print("正在读取完整的Shapefile...")
        gdf = gpd.read_file('2D_CELL.shp')
        print(f"Shapefile中的网格数: {len(gdf)}")
        print(f"Shapefile列名: {list(gdf.columns)}")

        # 检查网格ID字段
        if 'NAME' in gdf.columns:
            cell_id_field = 'NAME'
        elif 'JUNNAME' in gdf.columns:
            cell_id_field = 'JUNNAME'
        else:
            raise ValueError("找不到网格ID字段")

        print(f"使用字段 '{cell_id_field}' 作为网格ID")

        # 筛选出CSV数据中包含的网格
        print("正在筛选匹配的网格...")
        matched_gdf = gdf[gdf[cell_id_field].isin(csv_cells)].copy()
        print(f"匹配的网格数: {len(matched_gdf)}")

        if len(matched_gdf) == 0:
            print("警告: 没有找到匹配的网格!")
            return False

        # 重命名cell_id字段并添加必要的属性
        matched_gdf['cell_id'] = matched_gdf[cell_id_field]

        # 计算原始坐标 (如果需要)
        matched_gdf['original_x'] = matched_gdf.geometry.centroid.x
        matched_gdf['original_y'] = matched_gdf.geometry.centroid.y

        # 转换为WGS84坐标系 (如果不是的话)
        if matched_gdf.crs != 'EPSG:4326':
            print(f"转换坐标系从 {matched_gdf.crs} 到 EPSG:4326...")
            matched_gdf = matched_gdf.to_crs('EPSG:4326')

        # 只保留需要的字段
        columns_to_keep = ['cell_id', 'original_x', 'original_y', 'geometry']
        if 'ELEVATION' in matched_gdf.columns:
            matched_gdf['elevation'] = matched_gdf['ELEVATION']
            columns_to_keep.append('elevation')
        if 'AREA' in matched_gdf.columns:
            matched_gdf['area'] = matched_gdf['AREA']
            columns_to_keep.append('area')

        final_gdf = matched_gdf[columns_to_keep].copy()

        # 保存为GeoJSON
        os.makedirs('processed_data', exist_ok=True)
        output_file = 'processed_data/full_geometry.geojson'

        print(f"正在保存到 {output_file}...")
        final_gdf.to_file(output_file, driver='GeoJSON')

        print(f"✓ 完整几何数据已保存")
        print(f"  - 网格数: {len(final_gdf)}")
        print(f"  - 坐标系: {final_gdf.crs}")
        print(f"  - 边界框: {final_gdf.total_bounds}")

        # 生成统计信息
        stats = {
            'total_cells': len(final_gdf),
            'coordinate_system': str(final_gdf.crs),
            'bounds': {
                'minx': float(final_gdf.total_bounds[0]),
                'miny': float(final_gdf.total_bounds[1]),
                'maxx': float(final_gdf.total_bounds[2]),
                'maxy': float(final_gdf.total_bounds[3])
            },
            'sample_cells': final_gdf['cell_id'].head(10).tolist()
        }

        with open('processed_data/full_geometry_stats.json', 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        print(f"✓ 几何数据统计已保存到 processed_data/full_geometry_stats.json")

        return True

    except Exception as e:
        print(f"生成完整几何数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🗺️ 完整几何数据生成器")
    print("=" * 50)

    if generate_full_geometry():
        print("\n✓ 完整几何数据生成完成!")
    else:
        print("\n❌ 完整几何数据生成失败!")
        exit(1)

    print("\n生成的文件:")
    print("  - processed_data/full_geometry.geojson (完整几何数据)")
    print("  - processed_data/full_geometry_stats.json (几何数据统计)")