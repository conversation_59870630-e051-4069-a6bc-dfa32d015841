{"2025-04-30T00:30:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.064, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "velocities": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.059, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "2025-04-30T00:35:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.076, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "velocities": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.113, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "2025-04-30T00:40:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.095, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "velocities": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.216, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "2025-04-30T00:45:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.054, 0.118, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.056, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "velocities": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.964, 0.33, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.078, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "2025-04-30T00:50:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.0, 0.0, 0.0, 0.088, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.079, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.073, 0.141, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.054, 0.075, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "velocities": [0.0, 0.0, 0.0, 0.211, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.033, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.195, 0.465, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.854, 2.725, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "2025-04-30T00:55:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.053, 0.0, 0.0, 0.161, 0.075, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.122, 0.0, 0.0, 0.052, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.062, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.063, 0.0, 0.056, 0.097, 0.171, 0.0, 0.059, 0.071, 0.0, 0.0, 0.0, 0.072, 0.103, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "velocities": [1.015, 0.0, 0.0, 0.334, 0.778, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.034, 0.0, 0.0, 0.012, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.027, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.479, 0.0, 2.509, 1.462, 0.642, 0.0, 3.071, 2.629, 0.0, 0.0, 0.0, 2.329, 3.45, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "2025-04-30T01:00:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.187, 0.063, 0.072, 0.196, 0.121, 0.083, 0.061, 0.0, 0.051, 0.0, 0.054, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.198, 0.0, 0.0, 0.073, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.078, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.102, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.053, 0.0, 0.0, 0.0, 0.0, 0.065, 0.0, 0.0, 0.0, 0.059, 0.0, 0.0, 0.085, 0.054, 0.087, 0.135, 0.218, 0.065, 0.091, 0.108, 0.0, 0.0, 0.0, 0.102, 0.151, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "velocities": [0.522, 1.464, 0.999, 0.671, 1.206, 1.042, 0.34, 0.0, 0.951, 0.0, 2.78, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.039, 0.0, 0.0, 0.022, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.034, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.031, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.032, 0.0, 0.0, 0.0, 0.0, 0.029, 0.0, 0.0, 0.0, 0.253, 0.0, 0.0, 0.692, 1.601, 2.85, 1.828, 0.916, 2.316, 3.831, 3.305, 0.0, 0.0, 0.0, 3.117, 4.632, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "2025-04-30T01:05:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.377, 0.238, 0.22, 0.276, 0.261, 0.268, 0.2, 0.17, 0.131, 0.067, 0.066, 0.0, 0.151, 0.121, 0.108, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.075, 0.0, 0.064, 0.131, 0.319, 0.0, 0.0, 0.168, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.068, 0.123, 0.0, 0.058, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.165, 0.0, 0.0, 0.16, 0.0, 0.0, 0.0, 0.132, 0.0, 0.063, 0.065, 0.077, 0.065, 0.108, 0.096, 0.0, 0.099, 0.131, 0.07, 0.084, 0.127, 0.071, 0.096, 0.125, 0.091, 0.129, 0.153, 0.238, 0.094, 0.11, 0.125, 0.0, 0.0, 0.0, 0.115, 0.172, 0.056, 0.068, 0.0, 0.0, 0.088, 0.0, 0.0, 0.074, 0.115, 0.125, 0.123, 0.126, 0.0, 0.0, 0.0, 0.0, 0.0], "velocities": [0.21, 0.333, 0.309, 0.634, 0.691, 0.336, 0.189, 0.195, 0.583, 1.066, 3.157, 0.0, 0.152, 0.105, 0.172, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.241, 0.0, 0.534, 0.198, 0.03, 0.0, 0.0, 0.282, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.028, 0.006, 0.0, 0.023, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.024, 0.0, 0.0, 0.07, 0.0, 0.0, 0.0, 0.161, 0.0, 0.451, 0.081, 0.025, 0.042, 0.251, 0.35, 0.0, 0.019, 0.663, 1.718, 1.377, 0.703, 1.794, 1.217, 1.08, 2.102, 2.142, 1.981, 1.037, 2.518, 3.946, 3.55, 0.0, 0.0, 0.0, 3.425, 5.104, 2.087, 0.881, 0.0, 0.0, 0.504, 0.0, 0.0, 0.041, 0.035, 0.051, 1.029, 0.525, 0.0, 0.0, 0.0, 0.0, 0.0]}, "2025-04-30T01:10:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.53, 0.39, 0.0, 0.422, 0.41, 0.42, 0.351, 0.321, 0.282, 0.213, 0.113, 0.0, 0.301, 0.271, 0.262, 0.174, 0.067, 0.0, 0.0, 0.081, 0.122, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.171, 0.055, 0.16, 0.229, 0.435, 0.151, 0.0, 0.271, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.09, 0.125, 0.0, 0.075, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.145, 0.637, 0.078, 0.087, 0.196, 0.07, 0.271, 0.053, 0.139, 0.055, 0.079, 0.197, 0.077, 0.097, 0.118, 0.102, 0.053, 0.105, 0.131, 0.068, 0.081, 0.123, 0.066, 0.091, 0.12, 0.086, 0.12, 0.136, 0.216, 0.101, 0.091, 0.106, 0.077, 0.095, 0.06, 0.101, 0.149, 0.0, 0.065, 0.094, 0.162, 0.201, 0.142, 0.0, 0.1, 0.1, 0.11, 0.11, 0.116, 0.0, 0.203, 0.053, 0.06, 0.06], "velocities": [0.14, 0.168, 0.0, 0.315, 0.315, 0.174, 0.077, 0.073, 0.229, 0.209, 1.5, 0.0, 0.068, 0.033, 0.088, 0.131, 0.33, 0.0, 0.0, 0.163, 0.177, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.194, 2.062, 0.353, 0.198, 0.016, 0.355, 0.0, 0.133, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.014, 0.019, 0.0, 0.011, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.71, 0.029, 1.304, 1.126, 0.039, 1.591, 0.393, 1.326, 0.215, 1.006, 0.583, 0.1, 0.017, 0.224, 0.312, 0.371, 0.01, 0.019, 0.656, 1.678, 1.327, 0.659, 1.731, 1.15, 1.017, 2.031, 1.898, 1.819, 0.925, 1.532, 3.739, 3.29, 0.361, 0.273, 0.804, 3.069, 4.548, 0.0, 0.741, 0.432, 0.225, 0.34, 0.436, 0.0, 0.006, 0.006, 0.008, 0.784, 0.443, 0.0, 0.163, 0.066, 0.015, 0.011]}, "2025-04-30T01:15:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.634, 0.492, 0.0, 0.524, 0.512, 0.523, 0.452, 0.423, 0.384, 0.314, 0.215, 0.0, 0.404, 0.373, 0.365, 0.274, 0.164, 0.068, 0.0, 0.184, 0.224, 0.124, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.091, 0.116, 0.213, 0.255, 0.277, 0.158, 0.414, 0.291, 0.409, 0.477, 0.512, 0.398, 0.248, 0.518, 0.0, 0.0, 0.218, 0.0, 0.0, 0.0, 0.055, 0.104, 0.124, 0.053, 0.086, 0.0, 0.1, 0.113, 0.109, 0.055, 0.147, 0.064, 0.088, 0.0, 0.0, 0.369, 0.896, 0.246, 0.153, 0.192, 0.079, 0.28, 0.058, 0.133, 0.056, 0.079, 0.196, 0.076, 0.096, 0.114, 0.096, 0.06, 0.104, 0.119, 0.058, 0.072, 0.112, 0.054, 0.075, 0.101, 0.065, 0.096, 0.119, 0.195, 0.066, 0.075, 0.089, 0.111, 0.131, 0.101, 0.086, 0.126, 0.0, 0.078, 0.13, 0.201, 0.241, 0.181, 0.062, 0.102, 0.099, 0.109, 0.108, 0.119, 0.052, 0.222, 0.058, 0.07, 0.07], "velocities": [0.093, 0.118, 0.0, 0.197, 0.181, 0.114, 0.051, 0.067, 0.136, 0.118, 0.589, 0.0, 0.058, 0.028, 0.052, 0.075, 0.206, 0.542, 0.0, 0.036, 0.074, 0.145, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.261, 0.292, 0.279, 0.128, 0.212, 0.178, 0.163, 0.111, 0.092, 0.115, 0.009, 0.102, 0.083, 0.057, 0.0, 0.0, 0.068, 0.0, 0.0, 0.0, 0.007, 0.008, 0.011, 0.006, 0.006, 0.0, 0.585, 0.162, 0.135, 0.239, 0.275, 0.154, 0.345, 0.0, 0.0, 0.257, 0.078, 0.369, 0.769, 0.023, 1.671, 0.443, 1.416, 0.179, 0.993, 0.581, 0.094, 0.011, 0.22, 0.286, 0.33, 0.006, 0.007, 0.554, 1.505, 1.192, 0.527, 1.496, 0.99, 0.796, 1.682, 1.994, 1.65, 0.792, 1.659, 3.442, 2.972, 0.155, 0.126, 0.114, 2.742, 4.012, 0.0, 0.218, 0.137, 0.123, 0.162, 0.219, 0.64, 0.004, 0.023, 0.026, 0.468, 0.163, 0.504, 0.122, 0.039, 0.009, 0.006]}, "2025-04-30T01:20:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.714, 0.573, 0.0, 0.602, 0.593, 0.604, 0.533, 0.502, 0.462, 0.392, 0.291, 0.0, 0.482, 0.452, 0.442, 0.352, 0.241, 0.142, 0.0, 0.262, 0.302, 0.201, 0.092, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.077, 0.078, 0.0, 0.197, 0.166, 0.248, 0.268, 0.368, 0.407, 0.427, 0.307, 0.567, 0.437, 0.557, 0.627, 0.563, 0.546, 0.396, 0.667, 0.0, 0.196, 0.366, 0.0, 0.0, 0.085, 0.059, 0.114, 0.123, 0.058, 0.093, 0.0, 0.16, 0.172, 0.163, 0.114, 0.203, 0.124, 0.145, 0.11, 0.128, 0.428, 0.939, 0.303, 0.209, 0.188, 0.083, 0.283, 0.058, 0.12, 0.051, 0.074, 0.188, 0.075, 0.083, 0.097, 0.078, 0.061, 0.103, 0.092, 0.0, 0.0, 0.086, 0.0, 0.052, 0.078, 0.0, 0.062, 0.105, 0.178, 0.0, 0.064, 0.075, 0.107, 0.128, 0.098, 0.076, 0.109, 0.0, 0.077, 0.127, 0.198, 0.238, 0.178, 0.059, 0.095, 0.096, 0.106, 0.106, 0.116, 0.0, 0.218, 0.0, 0.077, 0.077], "velocities": [0.07, 0.081, 0.0, 0.138, 0.116, 0.077, 0.036, 0.05, 0.094, 0.09, 0.323, 0.0, 0.047, 0.022, 0.041, 0.076, 0.173, 0.29, 0.0, 0.038, 0.066, 0.126, 0.403, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.535, 0.459, 0.0, 0.239, 0.374, 0.139, 0.099, 0.134, 0.126, 0.131, 0.092, 0.138, 0.102, 0.073, 0.1, 0.006, 0.069, 0.081, 0.068, 0.0, 0.088, 0.074, 0.0, 0.0, 0.048, 0.005, 0.005, 0.008, 0.004, 0.004, 0.0, 0.646, 0.232, 0.176, 0.157, 0.321, 0.292, 0.339, 0.346, 0.292, 0.25, 0.098, 0.37, 0.547, 0.009, 1.537, 0.422, 1.382, 0.098, 0.946, 0.531, 0.052, 0.008, 0.141, 0.189, 0.212, 0.004, 0.005, 0.364, 0.0, 0.0, 0.333, 0.0, 0.838, 0.478, 0.0, 2.596, 1.518, 0.688, 0.0, 3.147, 2.756, 0.166, 0.128, 0.105, 2.429, 3.573, 0.0, 0.171, 0.21, 0.141, 0.141, 0.177, 0.491, 0.003, 0.008, 0.002, 0.269, 0.038, 0.0, 0.083, 0.0, 0.006, 0.004]}, "2025-04-30T01:25:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.773, 0.633, 0.0, 0.663, 0.653, 0.663, 0.593, 0.563, 0.523, 0.453, 0.353, 0.073, 0.543, 0.513, 0.503, 0.413, 0.303, 0.202, 0.093, 0.323, 0.363, 0.263, 0.152, 0.055, 0.0, 0.102, 0.0, 0.0, 0.0, 0.064, 0.0, 0.154, 0.155, 0.085, 0.274, 0.243, 0.324, 0.344, 0.443, 0.484, 0.503, 0.383, 0.644, 0.514, 0.634, 0.703, 0.601, 0.623, 0.473, 0.743, 0.094, 0.274, 0.443, 0.0, 0.0, 0.163, 0.062, 0.121, 0.123, 0.061, 0.098, 0.066, 0.182, 0.194, 0.184, 0.135, 0.225, 0.144, 0.165, 0.126, 0.137, 0.446, 0.963, 0.324, 0.227, 0.185, 0.074, 0.274, 0.054, 0.113, 0.0, 0.065, 0.18, 0.075, 0.075, 0.086, 0.063, 0.061, 0.103, 0.066, 0.0, 0.0, 0.051, 0.0, 0.0, 0.062, 0.0, 0.055, 0.094, 0.165, 0.0, 0.055, 0.066, 0.078, 0.099, 0.069, 0.067, 0.096, 0.0, 0.0, 0.098, 0.169, 0.209, 0.15, 0.0, 0.085, 0.072, 0.082, 0.082, 0.092, 0.0, 0.19, 0.0, 0.081, 0.081], "velocities": [0.048, 0.059, 0.0, 0.103, 0.08, 0.052, 0.028, 0.038, 0.069, 0.066, 0.207, 0.131, 0.034, 0.016, 0.028, 0.054, 0.116, 0.168, 0.397, 0.023, 0.045, 0.088, 0.246, 0.645, 0.0, 0.209, 0.0, 0.0, 0.0, 1.924, 0.0, 0.371, 0.309, 0.144, 0.184, 0.241, 0.141, 0.105, 0.114, 0.138, 0.104, 0.08, 0.144, 0.103, 0.069, 0.104, 0.004, 0.076, 0.135, 0.081, 0.859, 0.102, 0.077, 0.0, 0.0, 0.042, 0.003, 0.004, 0.006, 0.003, 0.003, 1.87, 0.756, 0.251, 0.177, 0.147, 0.314, 0.261, 0.254, 0.287, 0.186, 0.249, 0.107, 0.322, 0.475, 0.005, 1.625, 0.408, 1.346, 0.055, 0.0, 0.46, 0.033, 0.006, 0.088, 0.119, 0.11, 0.003, 0.006, 0.135, 0.0, 0.0, 0.138, 0.0, 0.0, 0.475, 0.0, 2.422, 1.411, 0.608, 0.0, 2.952, 2.53, 0.191, 0.115, 0.093, 2.229, 3.244, 0.0, 0.0, 0.385, 0.136, 0.133, 0.148, 0.0, 0.003, 0.003, 0.01, 0.117, 0.062, 0.0, 0.051, 0.0, 0.004, 0.006]}, "2025-04-30T01:30:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.823, 0.683, 0.0, 0.713, 0.703, 0.714, 0.643, 0.613, 0.573, 0.503, 0.403, 0.123, 0.593, 0.563, 0.553, 0.463, 0.353, 0.253, 0.143, 0.373, 0.413, 0.313, 0.203, 0.104, 0.0, 0.154, 0.055, 0.0, 0.066, 0.099, 0.0, 0.196, 0.196, 0.126, 0.316, 0.285, 0.366, 0.386, 0.486, 0.525, 0.546, 0.426, 0.686, 0.556, 0.676, 0.746, 0.629, 0.666, 0.516, 0.786, 0.135, 0.316, 0.485, 0.055, 0.086, 0.206, 0.065, 0.123, 0.123, 0.063, 0.102, 0.053, 0.163, 0.174, 0.164, 0.115, 0.205, 0.125, 0.144, 0.104, 0.111, 0.424, 0.945, 0.305, 0.206, 0.184, 0.069, 0.267, 0.0, 0.108, 0.0, 0.055, 0.174, 0.075, 0.067, 0.072, 0.0, 0.061, 0.103, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.057, 0.0, 0.0, 0.085, 0.154, 0.0, 0.0, 0.058, 0.0, 0.0, 0.0, 0.061, 0.086, 0.0, 0.0, 0.0, 0.091, 0.126, 0.065, 0.0, 0.084, 0.0, 0.0, 0.0, 0.0, 0.0, 0.162, 0.0, 0.081, 0.081], "velocities": [0.038, 0.046, 0.0, 0.085, 0.058, 0.039, 0.022, 0.03, 0.055, 0.053, 0.154, 0.104, 0.026, 0.011, 0.022, 0.045, 0.095, 0.124, 0.243, 0.016, 0.037, 0.071, 0.177, 0.325, 0.0, 0.134, 0.379, 0.0, 0.197, 1.246, 0.0, 0.296, 0.253, 0.137, 0.15, 0.189, 0.14, 0.093, 0.107, 0.128, 0.092, 0.071, 0.134, 0.098, 0.065, 0.099, 0.003, 0.072, 0.126, 0.079, 0.66, 0.095, 0.073, 0.093, 0.036, 0.046, 0.002, 0.001, 0.006, 0.002, 0.002, 1.766, 0.666, 0.209, 0.125, 0.124, 0.27, 0.204, 0.193, 0.21, 0.104, 0.203, 0.083, 0.262, 0.462, 0.003, 1.538, 0.369, 0.0, 0.022, 0.0, 0.381, 0.012, 0.005, 0.039, 0.018, 0.0, 0.002, 0.004, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.43, 0.0, 0.0, 1.319, 0.542, 0.0, 0.0, 2.388, 0.0, 0.0, 0.0, 2.039, 2.983, 0.0, 0.0, 0.0, 0.076, 0.124, 0.005, 0.0, 0.002, 0.0, 0.0, 0.0, 0.0, 0.0, 0.002, 0.0, 0.003, 0.005]}, "2025-04-30T01:35:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.864, 0.724, 0.0, 0.754, 0.744, 0.754, 0.684, 0.654, 0.614, 0.544, 0.444, 0.164, 0.634, 0.604, 0.594, 0.504, 0.394, 0.294, 0.184, 0.414, 0.454, 0.354, 0.244, 0.145, 0.0, 0.194, 0.094, 0.0, 0.088, 0.119, 0.0, 0.218, 0.218, 0.148, 0.337, 0.307, 0.387, 0.407, 0.507, 0.547, 0.567, 0.447, 0.707, 0.577, 0.697, 0.767, 0.652, 0.687, 0.537, 0.807, 0.157, 0.337, 0.507, 0.077, 0.106, 0.227, 0.067, 0.123, 0.123, 0.065, 0.106, 0.0, 0.133, 0.144, 0.134, 0.084, 0.174, 0.094, 0.114, 0.071, 0.0, 0.394, 0.913, 0.273, 0.174, 0.183, 0.058, 0.253, 0.0, 0.104, 0.0, 0.0, 0.172, 0.074, 0.063, 0.071, 0.0, 0.061, 0.102, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.053, 0.0, 0.0, 0.077, 0.145, 0.0, 0.0, 0.053, 0.0, 0.0, 0.0, 0.056, 0.078, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.083, 0.0, 0.0, 0.0, 0.0, 0.0, 0.134, 0.0, 0.081, 0.081], "velocities": [0.041, 0.046, 0.0, 0.077, 0.05, 0.04, 0.019, 0.031, 0.05, 0.049, 0.129, 0.094, 0.029, 0.017, 0.021, 0.044, 0.086, 0.104, 0.18, 0.019, 0.035, 0.064, 0.15, 0.231, 0.0, 0.105, 0.23, 0.0, 0.192, 1.016, 0.0, 0.272, 0.235, 0.155, 0.144, 0.181, 0.152, 0.104, 0.109, 0.136, 0.097, 0.084, 0.142, 0.109, 0.073, 0.107, 0.002, 0.078, 0.144, 0.092, 0.677, 0.106, 0.085, 0.18, 0.255, 0.069, 0.002, 0.001, 0.005, 0.002, 0.002, 0.0, 0.49, 0.153, 0.073, 0.066, 0.221, 0.157, 0.162, 0.504, 0.0, 0.134, 0.054, 0.186, 0.411, 0.001, 1.362, 0.287, 0.0, 0.008, 0.0, 0.0, 0.003, 0.004, 0.013, 0.003, 0.0, 0.002, 0.002, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.392, 0.0, 0.0, 1.249, 0.488, 0.0, 0.0, 2.21, 0.0, 0.0, 0.0, 1.893, 2.773, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.002, 0.0, 0.0, 0.0, 0.0, 0.0, 0.002, 0.0, 0.003, 0.004]}, "2025-04-30T01:40:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.898, 0.758, 0.0, 0.788, 0.778, 0.788, 0.718, 0.688, 0.648, 0.578, 0.478, 0.198, 0.668, 0.638, 0.629, 0.539, 0.428, 0.328, 0.218, 0.449, 0.489, 0.389, 0.279, 0.179, 0.081, 0.23, 0.129, 0.0, 0.088, 0.12, 0.0, 0.218, 0.218, 0.148, 0.338, 0.308, 0.388, 0.408, 0.508, 0.548, 0.568, 0.448, 0.708, 0.578, 0.698, 0.768, 0.67, 0.688, 0.538, 0.808, 0.157, 0.338, 0.508, 0.077, 0.107, 0.228, 0.068, 0.123, 0.122, 0.067, 0.108, 0.0, 0.108, 0.119, 0.109, 0.059, 0.149, 0.069, 0.089, 0.0, 0.0, 0.369, 0.89, 0.249, 0.15, 0.182, 0.0, 0.238, 0.0, 0.101, 0.0, 0.0, 0.171, 0.074, 0.062, 0.07, 0.0, 0.06, 0.102, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.072, 0.138, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.052, 0.072, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.083, 0.0, 0.0, 0.0, 0.0, 0.0, 0.071, 0.0, 0.08, 0.08], "velocities": [0.038, 0.043, 0.0, 0.075, 0.044, 0.037, 0.019, 0.032, 0.049, 0.049, 0.118, 0.098, 0.031, 0.019, 0.023, 0.046, 0.086, 0.1, 0.154, 0.023, 0.038, 0.066, 0.145, 0.193, 0.349, 0.093, 0.184, 0.0, 0.2, 0.999, 0.0, 0.274, 0.237, 0.167, 0.147, 0.182, 0.158, 0.114, 0.115, 0.142, 0.102, 0.093, 0.149, 0.122, 0.08, 0.115, 0.002, 0.087, 0.163, 0.101, 0.809, 0.125, 0.093, 0.215, 0.267, 0.08, 0.002, 0.001, 0.004, 0.001, 0.001, 0.0, 0.435, 0.135, 0.076, 0.097, 0.19, 0.15, 0.162, 0.0, 0.0, 0.116, 0.047, 0.154, 0.316, 0.001, 0.0, 0.197, 0.0, 0.004, 0.0, 0.0, 0.002, 0.003, 0.007, 0.002, 0.0, 0.002, 0.002, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.165, 0.443, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.77, 2.564, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.001, 0.0, 0.0, 0.0, 0.0, 0.0, 0.003, 0.0, 0.002, 0.003]}, "2025-04-30T01:45:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.926, 0.786, 0.0, 0.817, 0.806, 0.816, 0.747, 0.717, 0.677, 0.607, 0.506, 0.226, 0.697, 0.667, 0.657, 0.566, 0.456, 0.356, 0.247, 0.477, 0.516, 0.416, 0.306, 0.207, 0.107, 0.256, 0.157, 0.059, 0.072, 0.104, 0.0, 0.202, 0.202, 0.132, 0.322, 0.292, 0.372, 0.392, 0.492, 0.532, 0.552, 0.432, 0.692, 0.562, 0.682, 0.752, 0.684, 0.672, 0.522, 0.792, 0.141, 0.322, 0.492, 0.062, 0.092, 0.212, 0.07, 0.122, 0.122, 0.068, 0.111, 0.0, 0.087, 0.099, 0.089, 0.0, 0.129, 0.0, 0.069, 0.0, 0.0, 0.349, 0.87, 0.229, 0.129, 0.182, 0.0, 0.223, 0.0, 0.101, 0.0, 0.0, 0.171, 0.074, 0.062, 0.07, 0.0, 0.06, 0.102, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.067, 0.132, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.066, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.082, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.08, 0.08], "velocities": [0.032, 0.037, 0.0, 0.061, 0.035, 0.031, 0.015, 0.025, 0.039, 0.039, 0.09, 0.069, 0.023, 0.014, 0.016, 0.036, 0.068, 0.078, 0.114, 0.013, 0.027, 0.049, 0.11, 0.148, 0.248, 0.069, 0.154, 0.649, 0.205, 1.134, 0.0, 0.291, 0.251, 0.169, 0.152, 0.186, 0.159, 0.12, 0.119, 0.144, 0.106, 0.1, 0.151, 0.131, 0.084, 0.118, 0.001, 0.096, 0.174, 0.105, 0.992, 0.137, 0.094, 0.186, 0.066, 0.07, 0.001, 0.001, 0.004, 0.001, 0.001, 0.0, 0.437, 0.125, 0.074, 0.0, 0.182, 0.0, 0.185, 0.0, 0.0, 0.097, 0.039, 0.132, 0.207, 0.0, 0.0, 0.115, 0.0, 0.003, 0.0, 0.0, 0.001, 0.003, 0.005, 0.002, 0.0, 0.001, 0.002, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.099, 0.404, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.398, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.001, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.002, 0.002]}, "2025-04-30T01:50:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.951, 0.811, 0.0, 0.841, 0.831, 0.841, 0.771, 0.741, 0.701, 0.631, 0.531, 0.251, 0.721, 0.691, 0.681, 0.591, 0.481, 0.381, 0.271, 0.501, 0.541, 0.441, 0.331, 0.231, 0.132, 0.281, 0.182, 0.082, 0.0, 0.075, 0.0, 0.17, 0.17, 0.1, 0.29, 0.26, 0.34, 0.36, 0.46, 0.5, 0.52, 0.4, 0.66, 0.53, 0.65, 0.72, 0.696, 0.64, 0.49, 0.76, 0.109, 0.29, 0.46, 0.0, 0.059, 0.18, 0.071, 0.122, 0.122, 0.069, 0.112, 0.0, 0.057, 0.072, 0.063, 0.0, 0.104, 0.0, 0.0, 0.0, 0.0, 0.324, 0.843, 0.204, 0.104, 0.181, 0.0, 0.214, 0.0, 0.101, 0.0, 0.0, 0.171, 0.074, 0.061, 0.07, 0.0, 0.06, 0.102, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.062, 0.126, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.062, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.082, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.08, 0.08], "velocities": [0.028, 0.033, 0.0, 0.058, 0.029, 0.027, 0.014, 0.024, 0.038, 0.037, 0.082, 0.065, 0.022, 0.013, 0.016, 0.035, 0.065, 0.072, 0.099, 0.013, 0.026, 0.048, 0.104, 0.136, 0.19, 0.061, 0.123, 0.436, 0.0, 1.517, 0.0, 0.345, 0.294, 0.205, 0.169, 0.208, 0.173, 0.139, 0.131, 0.156, 0.117, 0.116, 0.163, 0.15, 0.095, 0.13, 0.001, 0.112, 0.209, 0.116, 1.444, 0.168, 0.104, 0.0, 0.066, 0.078, 0.001, 0.001, 0.003, 0.001, 0.001, 0.0, 0.675, 0.147, 0.07, 0.0, 0.225, 0.0, 0.0, 0.0, 0.0, 0.088, 0.038, 0.12, 0.163, 0.0, 0.0, 0.076, 0.0, 0.002, 0.0, 0.0, 0.001, 0.002, 0.004, 0.001, 0.0, 0.001, 0.002, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.045, 0.373, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.229, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.001, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.002, 0.002]}, "2025-04-30T01:55:00": {"cells": ["J2081192D", "J2081222D", "J2081232D", "J2081262D", "J2081422D", "J2081432D", "J2081442D", "J2081452D", "J2081462D", "J2081472D", "J2081482D", "J2081492D", "J2081622D", "J2081632D", "J2081642D", "J2081652D", "J2081662D", "J2081672D", "J2081682D", "J2081842D", "J2081852D", "J2081862D", "J2081872D", "J2081882D", "J2081892D", "J2082052D", "J2082062D", "J2082072D", "J2082162D", "J2082172D", "J2082272D", "J2082372D", "J2082382D", "J2082392D", "J2082572D", "J2082582D", "J2082592D", "J2082772D", "J2082782D", "J2082792D", "J2082982D", "J2082992D", "J2083002D", "J2083192D", "J2083202D", "J2083212D", "J2083252D", "J2083402D", "J2083412D", "J2083422D", "J2083582D", "J2083592D", "J2083602D", "J2083762D", "J2083772D", "J2083782D", "J2083832D", "J2083882D", "J2084192D", "J2084382D", "J2084672D", "J2084712D", "J2084882D", "J2084892D", "J2084902D", "J2085032D", "J2085052D", "J2085062D", "J2085072D", "J2085082D", "J2085092D", "J2085222D", "J2085232D", "J2085242D", "J2085252D", "J2085282D", "J2085412D", "J2085432D", "J2085442D", "J2085472D", "J2085602D", "J2085622D", "J2085662D", "J2085742D", "J2085782D", "J2085822D", "J2086062D", "J2086132D", "J2086152D", "J2086202D", "J2086212D", "J2086222D", "J2086342D", "J2086352D", "J2086362D", "J2086482D", "J2086492D", "J2086502D", "J2086512D", "J2086522D", "J2086782D", "J2086792D", "J2086802D", "J2086982D", "J2086992D", "J2087002D", "J2087512D", "J2087532D", "J2087542D", "J2087702D", "J2087712D", "J2087722D", "J2087732D", "J2087742D", "J2087752D", "J2088312D", "J2088382D", "J2088392D", "J2088402D", "J2088412D", "J2088452D", "J2088462D", "J2088502D", "J2088772D", "J2088802D"], "depths": [0.973, 0.832, 0.0, 0.862, 0.852, 0.862, 0.792, 0.762, 0.722, 0.652, 0.552, 0.272, 0.742, 0.712, 0.702, 0.612, 0.503, 0.402, 0.292, 0.522, 0.563, 0.463, 0.353, 0.253, 0.153, 0.302, 0.203, 0.103, 0.0, 0.0, 0.055, 0.121, 0.121, 0.051, 0.241, 0.211, 0.291, 0.311, 0.411, 0.451, 0.471, 0.351, 0.611, 0.482, 0.602, 0.672, 0.707, 0.592, 0.442, 0.712, 0.062, 0.242, 0.412, 0.0, 0.0, 0.132, 0.072, 0.122, 0.122, 0.07, 0.114, 0.0, 0.0, 0.0, 0.0, 0.0, 0.067, 0.0, 0.0, 0.0, 0.0, 0.287, 0.807, 0.167, 0.066, 0.181, 0.0, 0.21, 0.0, 0.101, 0.0, 0.0, 0.171, 0.074, 0.061, 0.07, 0.0, 0.06, 0.101, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.058, 0.121, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.057, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.082, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.08, 0.08], "velocities": [0.023, 0.028, 0.0, 0.05, 0.022, 0.021, 0.011, 0.019, 0.032, 0.031, 0.066, 0.048, 0.016, 0.009, 0.013, 0.028, 0.053, 0.058, 0.076, 0.008, 0.019, 0.037, 0.082, 0.106, 0.144, 0.048, 0.097, 0.307, 0.0, 0.0, 0.275, 0.402, 0.326, 0.268, 0.181, 0.238, 0.168, 0.153, 0.139, 0.158, 0.125, 0.129, 0.167, 0.171, 0.102, 0.133, 0.001, 0.12, 0.243, 0.116, 2.343, 0.2, 0.102, 0.0, 0.0, 0.053, 0.001, 0.001, 0.003, 0.001, 0.001, 0.0, 0.0, 0.0, 0.0, 0.0, 0.158, 0.0, 0.0, 0.0, 0.0, 0.038, 0.015, 0.079, 0.177, 0.0, 0.0, 0.056, 0.0, 0.002, 0.0, 0.0, 0.001, 0.002, 0.003, 0.001, 0.0, 0.001, 0.001, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.99, 0.344, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.13, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.001, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.001, 0.002]}}