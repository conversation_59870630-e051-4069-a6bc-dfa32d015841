<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>MapLibre 固定热力图测试</title>
    <script src='https://unpkg.com/maplibre-gl@3.6.2/dist/maplibre-gl.js'></script>
    <link href='https://unpkg.com/maplibre-gl@3.6.2/dist/maplibre-gl.css' rel='stylesheet' />
    <style>
        body { margin: 0; padding: 0; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; }
        .controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
        .control-group {
            margin-bottom: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }
        select, button {
            width: 100%;
            padding: 4px;
            background: #333;
            color: white;
            border: 1px solid #555;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    
    <div class="controls">
        <div class="control-group">
            <label>渲染模式:</label>
            <select id="renderMode">
                <option value="grid">网格模式</option>
                <option value="heatmap">固定热力图</option>
            </select>
        </div>
        <div class="control-group">
            <button id="playBtn">播放</button>
        </div>
        <div class="control-group">
            <div id="info" style="font-size: 11px; color: #ccc;">
                当前缩放级别: <span id="zoomLevel">12</span><br>
                热力图参数: 固定不变
            </div>
        </div>
    </div>

    <script>
        // 初始化MapLibre地图
        const map = new maplibregl.Map({
            container: 'map',
            style: {
                version: 8,
                sources: {
                    'carto-dark': {
                        type: 'raster',
                        tiles: [
                            'https://a.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',
                            'https://b.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',
                            'https://c.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',
                            'https://d.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png'
                        ],
                        tileSize: 256
                    }
                },
                layers: [
                    {
                        id: 'carto-dark-layer',
                        type: 'raster',
                        source: 'carto-dark'
                    }
                ]
            },
            center: [108.9, 34.2],
            zoom: 12
        });

        let currentData = [];
        let currentMode = 'grid';

        // 生成测试数据
        function generateTestData() {
            const data = [];
            const centerLng = 108.9;
            const centerLat = 34.2;
            
            for (let i = 0; i < 1000; i++) {
                const lng = centerLng + (Math.random() - 0.5) * 0.1;
                const lat = centerLat + (Math.random() - 0.5) * 0.1;
                const depth = Math.random() * 5; // 0-5米水深
                
                data.push({
                    type: 'Feature',
                    geometry: {
                        type: 'Point',
                        coordinates: [lng, lat]
                    },
                    properties: {
                        depth: depth
                    }
                });
            }
            
            return {
                type: 'FeatureCollection',
                features: data
            };
        }

        map.on('load', () => {
            console.log('MapLibre地图加载完成');
            
            // 生成测试数据
            currentData = generateTestData();
            
            // 添加数据源
            map.addSource('flood-data', {
                type: 'geojson',
                data: currentData
            });
            
            // 添加网格图层
            map.addLayer({
                id: 'flood-grid',
                type: 'circle',
                source: 'flood-data',
                paint: {
                    'circle-radius': 8,
                    'circle-color': [
                        'interpolate',
                        ['linear'],
                        ['get', 'depth'],
                        0, '#0066cc',
                        1, '#00ff00',
                        2, '#ffff00',
                        3, '#ff9900',
                        5, '#ff0000'
                    ],
                    'circle-opacity': 0.8,
                    'circle-stroke-width': 1,
                    'circle-stroke-color': '#ffffff',
                    'circle-stroke-opacity': 0.5
                }
            });
            
            // 添加固定热力图图层
            map.addLayer({
                id: 'flood-heatmap',
                type: 'heatmap',
                source: 'flood-data',
                maxzoom: 24, // 允许在所有缩放级别显示
                paint: {
                    // 关键：固定半径，不随缩放变化
                    'heatmap-radius': 15, // 固定15像素半径
                    
                    // 关键：固定强度，不随缩放变化  
                    'heatmap-intensity': 1.0, // 固定强度
                    
                    // 权重基于深度
                    'heatmap-weight': [
                        'interpolate',
                        ['linear'],
                        ['get', 'depth'],
                        0, 0,
                        5, 1
                    ],
                    
                    // 颜色渐变
                    'heatmap-color': [
                        'interpolate',
                        ['linear'],
                        ['heatmap-density'],
                        0, 'rgba(0,0,0,0)',
                        0.1, '#0066cc',
                        0.3, '#00ff00', 
                        0.5, '#ffff00',
                        0.7, '#ff9900',
                        1, '#ff0000'
                    ],
                    
                    // 透明度
                    'heatmap-opacity': 0.8
                },
                layout: {
                    visibility: 'none' // 初始隐藏
                }
            });
            
            console.log('图层添加完成');
        });

        // 更新缩放级别显示
        map.on('zoom', () => {
            document.getElementById('zoomLevel').textContent = map.getZoom().toFixed(1);
        });

        // 渲染模式切换
        document.getElementById('renderMode').addEventListener('change', (e) => {
            const mode = e.target.value;
            currentMode = mode;
            
            if (mode === 'heatmap') {
                map.setLayoutProperty('flood-grid', 'visibility', 'none');
                map.setLayoutProperty('flood-heatmap', 'visibility', 'visible');
                console.log('切换到固定热力图模式');
            } else {
                map.setLayoutProperty('flood-grid', 'visibility', 'visible');
                map.setLayoutProperty('flood-heatmap', 'visibility', 'none');
                console.log('切换到网格模式');
            }
        });

        // 播放按钮（模拟数据更新）
        document.getElementById('playBtn').addEventListener('click', () => {
            console.log('开始播放动画');
            
            let frame = 0;
            const maxFrames = 10;
            
            const animate = () => {
                frame++;
                
                // 模拟数据变化
                const newData = generateTestData();
                map.getSource('flood-data').setData(newData);
                
                console.log(`播放第 ${frame} 帧`);
                
                if (frame < maxFrames) {
                    setTimeout(animate, 1000);
                } else {
                    console.log('动画播放完成');
                }
            };
            
            animate();
        });
    </script>
</body>
</html>
