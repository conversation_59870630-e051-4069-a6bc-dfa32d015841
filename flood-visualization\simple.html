<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化洪水可视化</title>
    <link href="https://unpkg.com/maplibre-gl@4.0.0/dist/maplibre-gl.css" rel="stylesheet">
    <style>
        body { margin: 0; font-family: Arial, sans-serif; }
        #map { width: 100%; height: 100vh; }
        #info { position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; z-index: 1000; }
    </style>
</head>
<body>
    <div id="info">正在初始化...</div>
    <div id="map"></div>

    <script src="https://unpkg.com/maplibre-gl@4.0.0/dist/maplibre-gl.js"></script>
    <script>
        const info = document.getElementById('info');

        function updateInfo(message) {
            info.innerHTML = message;
            console.log(message);
        }

        async function init() {
            try {
                updateInfo('初始化地图...');

                // 初始化地图
                const map = new maplibregl.Map({
                    container: 'map',
                    style: {
                        version: 8,
                        sources: {
                            'osm': {
                                type: 'raster',
                                tiles: ['https://tile.openstreetmap.org/{z}/{x}/{y}.png'],
                                tileSize: 256
                            }
                        },
                        layers: [{ id: 'osm', type: 'raster', source: 'osm' }]
                    },
                    center: [108.9, 34.2],
                    zoom: 12
                });

                map.on('load', async () => {
                    try {
                        updateInfo('地图加载完成，加载数据...');

                        // 加载几何数据
                        const response = await fetch('../processed_data/correct_geometry.geojson');
                        if (!response.ok) {
                            throw new Error('数据加载失败');
                        }

                        const data = await response.json();
                        updateInfo(`数据加载成功: ${data.features.length} 个网格`);

                        // 添加数据源
                        map.addSource('flood', {
                            type: 'geojson',
                            data: data
                        });

                        // 添加图层
                        map.addLayer({
                            id: 'flood-layer',
                            type: 'fill',
                            source: 'flood',
                            paint: {
                                'fill-color': '#ff0000',
                                'fill-opacity': 0.5,
                                'fill-outline-color': '#ffffff'
                            }
                        });

                        updateInfo('可视化完成！应该能看到红色网格');

                        // 缩放到数据范围
                        const bounds = new maplibregl.LngLatBounds();
                        data.features.forEach(feature => {
                            feature.geometry.coordinates[0].forEach(coord => {
                                bounds.extend(coord);
                            });
                        });
                        map.fitBounds(bounds, { padding: 50 });

                    } catch (error) {
                        updateInfo('错误: ' + error.message);
                        console.error(error);
                    }
                });

            } catch (error) {
                updateInfo('初始化失败: ' + error.message);
                console.error(error);
            }
        }

        init();
    </script>
</body>
</html>